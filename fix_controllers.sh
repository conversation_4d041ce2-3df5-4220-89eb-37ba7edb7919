#!/bin/bash

# 批量修复equipment模块controller的返回对象，统一使用FrResult

CONTROLLER_DIR="/Users/<USER>/equipment/admin/equipment/src/main/java/org/simple/equipment/controller"

# 需要修复的文件列表
FILES=(
    "EquipmentDashboardController.java"
    "EquipmentQrCodeController.java"
    "InspectionApprovalController.java"
    "InspectionTemplateController.java"
    "InspectionTemplateItemController.java"
    "IntelligentExceptionController.java"
    "IntelligentTemplateController.java"
    "NurseWorkspaceController.java"
    "OfflineSyncController.java"
    "PatrolPlanController.java"
    "TaskTypeController.java"
    "AssetTagController.java"
    "AssetFieldValueController.java"
    "AttributeTemplateController.java"
    "CustomFieldController.java"
)

echo "开始批量修复controller返回对象..."

for file in "${FILES[@]}"; do
    filepath="$CONTROLLER_DIR/$file"
    if [ -f "$filepath" ]; then
        echo "正在修复: $file"
        
        # 1. 替换导入语句
        sed -i '' 's/import org\.simple\.base\.util\.ResultUtil;/\/\/ import org.simple.base.util.ResultUtil; \/\/ 已替换为FrResult/g' "$filepath"
        sed -i '' 's/import org\.simple\.base\.vo\.Result;/import org.simple.base.vo.FrResult;/g' "$filepath"
        sed -i '' 's/import org\.simple\.base\.common\.R;/import org.simple.base.vo.FrResult;/g' "$filepath"
        
        # 2. 替换返回类型
        sed -i '' 's/public Result</public FrResult</g' "$filepath"
        sed -i '' 's/public R</public FrResult</g' "$filepath"
        
        # 3. 替换返回语句
        sed -i '' 's/ResultUtil\.success(/FrResult.success(/g' "$filepath"
        sed -i '' 's/ResultUtil\.error(/FrResult.failed(/g' "$filepath"
        sed -i '' 's/ResultUtil\.failed(/FrResult.failed(/g' "$filepath"
        sed -i '' 's/R\.ok(/FrResult.success(/g' "$filepath"
        sed -i '' 's/R\.failed(/FrResult.failed(/g' "$filepath"
        sed -i '' 's/R\.error(/FrResult.failed(/g' "$filepath"
        
        echo "完成修复: $file"
    else
        echo "文件不存在: $file"
    fi
done

echo "批量修复完成！"
echo ""
echo "修复内容："
echo "1. 导入语句: Result/R -> FrResult"
echo "2. 返回类型: Result<T>/R<T> -> FrResult<T>"
echo "3. 返回方法: ResultUtil.success()/R.ok() -> FrResult.success()"
echo "4. 错误返回: ResultUtil.error()/R.failed() -> FrResult.failed()"
