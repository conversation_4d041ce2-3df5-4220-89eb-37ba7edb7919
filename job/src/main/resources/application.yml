server:
  servlet:
    context-path: /xxl-job-admin
  port: 8081
### actuator
management:
  health:
    mail:
      enabled: false
  server:
    base-path: /actuator

### resources
spring:
  mvc:
    servlet:
      load-on-startup: 0
    static-path-pattern: /static/**
  freemarker:
    templateLoaderPath: classpath:/templates/
    suffix: .ftl
    charset: UTF-8
    request-context-attribute: request
    settings:
      number_format: 0.##########
  datasource:
    url: ********************************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 10
      maximum-pool-size: 30
      auto-commit: true
      idle-timeout: 30000
      pool-name: HikariCP
      max-lifetime: 900000
      connection-timeout: 10000
      connection-test-query: SELECT 1
      validation-timeout: 1000
  mail:
    host: smtp.qq.com
    port: 25
    username: <EMAIL>
    from: <EMAIL>
    password: xxx
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
          socketFactory:
            class: javax.net.ssl.SSLSocketFactory
  web:
    resources:
      static-locations: classpath:/static/
### mybatis-plus
mybatis:
  mapper-locations: classpath:/mybatis-mapper/*Mapper.xml
  #configuration:
  #  log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
#mybatis.type-aliases-package=com.xxl.job.admin.core.model

### xxl-job, access token
xxl:
  job:
    accessToken: 8WVT2qfjh3q3bNCX
    i18n: zh_CN
    triggerpool:
      fast:
        max: 200
      slow:
        max: 100
    logretentiondays: 30
    timeout: 3
