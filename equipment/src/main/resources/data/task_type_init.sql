-- 任务类型初始化数据
-- 插入系统内置任务类型

INSERT INTO equipment_task_type (id, type_code, type_name, description, is_system, status, tenant_id, create_date, creator) VALUES 
('sys_task_type_1', 'INSPECTION', '设备点检', '定期对设备进行点检，确保设备正常运行', '1', '0', 'default_tenant', NOW(), 'system'),
('sys_task_type_2', 'MAINTENANCE', '设备保养', '按计划对设备进行保养维护', '1', '0', 'default_tenant', NOW(), 'system'),
('sys_task_type_3', 'REPAIR', '设备维修', '设备故障时进行维修', '1', '0', 'default_tenant', NOW(), 'system'),
('sys_task_type_4', 'PATROL', '设备巡检', '对设备进行日常巡视检查', '1', '0', 'default_tenant', NOW(), 'system'),
('sys_task_type_5', 'CALIBRATION', '设备校准', '对计量设备进行校准', '1', '0', 'default_tenant', NOW(), 'system'),
('sys_task_type_6', 'INVENTORY', '设备盘点', '定期对设备进行盘点清查', '1', '0', 'default_tenant', NOW(), 'system'),
('sys_task_type_7', 'CLEANING', '设备清洁', '对设备进行清洁保洁', '1', '0', 'default_tenant', NOW(), 'system'),
('sys_task_type_8', 'TESTING', '设备测试', '对设备功能进行测试验证', '1', '0', 'default_tenant', NOW(), 'system');