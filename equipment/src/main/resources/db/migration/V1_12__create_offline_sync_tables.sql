-- 离线数据同步表
CREATE TABLE `offline_sync_records` (
    `id` VARCHAR(50) NOT NULL COMMENT '主键ID',
    `user_id` VARCHAR(50) NOT NULL COMMENT '用户ID',
    `device_id` VARCHAR(100) NOT NULL COMMENT '设备标识',
    `sync_type` ENUM('DOWNLOAD', 'UPLOAD') NOT NULL COMMENT '同步类型',
    `data_type` VARCHAR(50) NOT NULL COMMENT '数据类型',
    `entity_id` VARCHAR(50) COMMENT '实体ID',
    `entity_type` VARCHAR(50) COMMENT '实体类型',
    `operation` ENUM('CREATE', 'UPDATE', 'DELETE') NOT NULL COMMENT '操作类型',
    `data_content` LONGTEXT COMMENT '数据内容(JSON)',
    `local_timestamp` BIGINT NOT NULL COMMENT '本地时间戳',
    `server_timestamp` BIGINT COMMENT '服务器时间戳',
    `sync_status` ENUM('PENDING', 'SYNCED', 'CONFLICT', 'FAILED') DEFAULT 'PENDING' COMMENT '同步状态',
    `conflict_resolution` ENUM('SERVER_WINS', 'CLIENT_WINS', 'MERGE', 'MANUAL') COMMENT '冲突解决方式',
    `error_message` TEXT COMMENT '错误信息',
    `retry_count` INT DEFAULT 0 COMMENT '重试次数',
    `checksum` VARCHAR(64) COMMENT '数据校验和',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `tenant_id` VARCHAR(50) COMMENT '租户ID',
    PRIMARY KEY (`id`),
    INDEX `idx_user_device` (`user_id`, `device_id`),
    INDEX `idx_sync_status` (`sync_status`),
    INDEX `idx_data_type` (`data_type`),
    INDEX `idx_entity` (`entity_id`, `entity_type`),
    INDEX `idx_timestamp` (`local_timestamp`),
    INDEX `idx_tenant` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='离线数据同步记录表';

-- 离线任务缓存表
CREATE TABLE `offline_task_cache` (
    `id` VARCHAR(50) NOT NULL COMMENT '主键ID',
    `user_id` VARCHAR(50) NOT NULL COMMENT '用户ID',
    `device_id` VARCHAR(100) NOT NULL COMMENT '设备标识',
    `task_id` VARCHAR(50) NOT NULL COMMENT '任务ID',
    `task_type` VARCHAR(50) NOT NULL COMMENT '任务类型',
    `task_data` LONGTEXT NOT NULL COMMENT '任务数据(JSON)',
    `template_data` LONGTEXT COMMENT '模板数据(JSON)',
    `equipment_data` LONGTEXT COMMENT '设备数据(JSON)',
    `cache_timestamp` BIGINT NOT NULL COMMENT '缓存时间戳',
    `expiry_timestamp` BIGINT COMMENT '过期时间戳',
    `download_status` ENUM('DOWNLOADING', 'COMPLETED', 'FAILED') DEFAULT 'DOWNLOADING' COMMENT '下载状态',
    `last_access` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '最后访问时间',
    `access_count` INT DEFAULT 0 COMMENT '访问次数',
    `file_size` BIGINT COMMENT '文件大小(字节)',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `tenant_id` VARCHAR(50) COMMENT '租户ID',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_task` (`user_id`, `device_id`, `task_id`),
    INDEX `idx_user_device` (`user_id`, `device_id`),
    INDEX `idx_task_type` (`task_type`),
    INDEX `idx_expiry` (`expiry_timestamp`),
    INDEX `idx_download_status` (`download_status`),
    INDEX `idx_tenant` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='离线任务缓存表';

-- 数据冲突记录表
CREATE TABLE `data_conflict_records` (
    `id` VARCHAR(50) NOT NULL COMMENT '主键ID',
    `sync_record_id` VARCHAR(50) NOT NULL COMMENT '同步记录ID',
    `entity_id` VARCHAR(50) NOT NULL COMMENT '实体ID',
    `entity_type` VARCHAR(50) NOT NULL COMMENT '实体类型',
    `conflict_field` VARCHAR(100) COMMENT '冲突字段',
    `client_value` TEXT COMMENT '客户端值',
    `server_value` TEXT COMMENT '服务器值',
    `client_timestamp` BIGINT COMMENT '客户端时间戳',
    `server_timestamp` BIGINT COMMENT '服务器时间戳',
    `conflict_type` ENUM('VALUE_CONFLICT', 'DELETE_CONFLICT', 'VERSION_CONFLICT') NOT NULL COMMENT '冲突类型',
    `resolution_strategy` ENUM('AUTO_RESOLVE', 'MANUAL_RESOLVE') DEFAULT 'MANUAL_RESOLVE' COMMENT '解决策略',
    `resolved_value` TEXT COMMENT '解决后的值',
    `resolved_by` VARCHAR(50) COMMENT '解决人',
    `resolved_at` TIMESTAMP NULL COMMENT '解决时间',
    `resolution_reason` TEXT COMMENT '解决原因',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `tenant_id` VARCHAR(50) COMMENT '租户ID',
    PRIMARY KEY (`id`),
    INDEX `idx_sync_record` (`sync_record_id`),
    INDEX `idx_entity` (`entity_id`, `entity_type`),
    INDEX `idx_conflict_type` (`conflict_type`),
    INDEX `idx_resolution_strategy` (`resolution_strategy`),
    INDEX `idx_tenant` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据冲突记录表';

-- 离线用户会话表
CREATE TABLE `offline_user_sessions` (
    `id` VARCHAR(50) NOT NULL COMMENT '主键ID',
    `user_id` VARCHAR(50) NOT NULL COMMENT '用户ID',
    `device_id` VARCHAR(100) NOT NULL COMMENT '设备标识',
    `session_token` VARCHAR(255) NOT NULL COMMENT '会话令牌',
    `device_info` JSON COMMENT '设备信息',
    `app_version` VARCHAR(50) COMMENT '应用版本',
    `last_sync_time` TIMESTAMP NULL COMMENT '最后同步时间',
    `sync_data_size` BIGINT DEFAULT 0 COMMENT '同步数据大小',
    `pending_uploads` INT DEFAULT 0 COMMENT '待上传数量',
    `network_status` ENUM('ONLINE', 'OFFLINE', 'POOR') DEFAULT 'ONLINE' COMMENT '网络状态',
    `last_heartbeat` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '最后心跳时间',
    `is_active` TINYINT DEFAULT 1 COMMENT '是否活跃',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `tenant_id` VARCHAR(50) COMMENT '租户ID',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_device` (`user_id`, `device_id`),
    INDEX `idx_session_token` (`session_token`),
    INDEX `idx_last_sync` (`last_sync_time`),
    INDEX `idx_network_status` (`network_status`),
    INDEX `idx_tenant` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='离线用户会话表';

-- 网络状态监控表
CREATE TABLE `network_status_logs` (
    `id` VARCHAR(50) NOT NULL COMMENT '主键ID',
    `user_id` VARCHAR(50) NOT NULL COMMENT '用户ID',
    `device_id` VARCHAR(100) NOT NULL COMMENT '设备标识',
    `network_type` VARCHAR(50) COMMENT '网络类型(wifi, 4g, 5g等)',
    `signal_strength` INT COMMENT '信号强度',
    `connection_speed` DECIMAL(10,2) COMMENT '连接速度(Mbps)',
    `latency` INT COMMENT '延迟(ms)',
    `status_change_from` VARCHAR(20) COMMENT '状态变更前',
    `status_change_to` VARCHAR(20) COMMENT '状态变更后',
    `location_info` JSON COMMENT '位置信息',
    `recorded_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录时间',
    `tenant_id` VARCHAR(50) COMMENT '租户ID',
    PRIMARY KEY (`id`),
    INDEX `idx_user_device` (`user_id`, `device_id`),
    INDEX `idx_recorded_at` (`recorded_at`),
    INDEX `idx_network_type` (`network_type`),
    INDEX `idx_tenant` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='网络状态监控表';