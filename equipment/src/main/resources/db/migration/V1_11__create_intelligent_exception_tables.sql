-- 异常处理规则表
CREATE TABLE `exception_handling_rules` (
    `id` VARCHAR(50) NOT NULL COMMENT '主键ID',
    `rule_name` VARCHAR(100) NOT NULL COMMENT '规则名称',
    `equipment_type` VARCHAR(50) COMMENT '设备类型',
    `exception_keywords` JSON NOT NULL COMMENT '异常关键词',
    `exception_level` ENUM('LEVEL_1', 'LEVEL_2', 'LEVEL_3', 'LEVEL_4') NOT NULL COMMENT '异常等级',
    `auto_actions` JSON COMMENT '自动处理动作',
    `notification_rules` JSON COMMENT '通知规则',
    `escalation_rules` JSON COMMENT '升级规则',
    `priority` INT DEFAULT 0 COMMENT '优先级',
    `is_enabled` TINYINT DEFAULT 1 COMMENT '是否启用',
    `created_by` VARCHAR(50) COMMENT '创建人',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by` VARCHAR(50) COMMENT '更新人',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `tenant_id` VARCHAR(50) COMMENT '租户ID',
    `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除',
    PRIMARY KEY (`id`),
    INDEX `idx_equipment_type` (`equipment_type`),
    INDEX `idx_exception_level` (`exception_level`),
    INDEX `idx_priority` (`priority`),
    INDEX `idx_tenant` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='异常处理规则表';

-- 异常处理记录表
CREATE TABLE `exception_handling_records` (
    `id` VARCHAR(50) NOT NULL COMMENT '主键ID',
    `equipment_id` VARCHAR(50) NOT NULL COMMENT '设备ID',
    `inspection_task_id` VARCHAR(50) COMMENT '点检任务ID',
    `exception_description` TEXT NOT NULL COMMENT '异常描述',
    `exception_level` ENUM('LEVEL_1', 'LEVEL_2', 'LEVEL_3', 'LEVEL_4') NOT NULL COMMENT '异常等级',
    `exception_type` VARCHAR(100) COMMENT '异常类型',
    `detection_method` ENUM('MANUAL', 'AUTO', 'AI') DEFAULT 'MANUAL' COMMENT '检测方式',
    `detection_confidence` DECIMAL(5,4) COMMENT '检测置信度',
    `handling_status` ENUM('PENDING', 'IN_PROGRESS', 'RESOLVED', 'ESCALATED') DEFAULT 'PENDING' COMMENT '处理状态',
    `assigned_to` VARCHAR(50) COMMENT '分配给',
    `assigned_at` TIMESTAMP NULL COMMENT '分配时间',
    `resolved_by` VARCHAR(50) COMMENT '解决人',
    `resolved_at` TIMESTAMP NULL COMMENT '解决时间',
    `resolution_description` TEXT COMMENT '解决描述',
    `work_order_id` VARCHAR(50) COMMENT '工单ID',
    `notification_sent` TINYINT DEFAULT 0 COMMENT '是否已发送通知',
    `escalation_level` INT DEFAULT 0 COMMENT '升级级别',
    `deadline` TIMESTAMP NULL COMMENT '处理截止时间',
    `photos` JSON COMMENT '照片附件',
    `created_by` VARCHAR(50) COMMENT '创建人',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by` VARCHAR(50) COMMENT '更新人',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `tenant_id` VARCHAR(50) COMMENT '租户ID',
    `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除',
    PRIMARY KEY (`id`),
    INDEX `idx_equipment` (`equipment_id`),
    INDEX `idx_exception_level` (`exception_level`),
    INDEX `idx_handling_status` (`handling_status`),
    INDEX `idx_assigned_to` (`assigned_to`),
    INDEX `idx_deadline` (`deadline`),
    INDEX `idx_tenant` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='异常处理记录表';

-- 通知记录表
CREATE TABLE `exception_notification_records` (
    `id` VARCHAR(50) NOT NULL COMMENT '主键ID',
    `exception_record_id` VARCHAR(50) NOT NULL COMMENT '异常记录ID',
    `notification_type` ENUM('SMS', 'EMAIL', 'APP_PUSH', 'PHONE_CALL', 'WECHAT') NOT NULL COMMENT '通知类型',
    `recipient_id` VARCHAR(50) NOT NULL COMMENT '接收人ID',
    `recipient_name` VARCHAR(100) COMMENT '接收人姓名',
    `recipient_contact` VARCHAR(100) COMMENT '接收人联系方式',
    `notification_content` TEXT COMMENT '通知内容',
    `send_status` ENUM('PENDING', 'SENT', 'FAILED', 'DELIVERED') DEFAULT 'PENDING' COMMENT '发送状态',
    `send_time` TIMESTAMP NULL COMMENT '发送时间',
    `delivery_time` TIMESTAMP NULL COMMENT '送达时间',
    `failure_reason` VARCHAR(500) COMMENT '失败原因',
    `retry_count` INT DEFAULT 0 COMMENT '重试次数',
    `escalation_level` INT DEFAULT 0 COMMENT '升级级别',
    `created_by` VARCHAR(50) COMMENT '创建人',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `tenant_id` VARCHAR(50) COMMENT '租户ID',
    PRIMARY KEY (`id`),
    INDEX `idx_exception_record` (`exception_record_id`),
    INDEX `idx_recipient` (`recipient_id`),
    INDEX `idx_send_status` (`send_status`),
    INDEX `idx_notification_type` (`notification_type`),
    INDEX `idx_tenant` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='异常通知记录表';

-- 工单自动生成表
CREATE TABLE `auto_work_orders` (
    `id` VARCHAR(50) NOT NULL COMMENT '主键ID',
    `exception_record_id` VARCHAR(50) NOT NULL COMMENT '异常记录ID',
    `work_order_number` VARCHAR(100) NOT NULL COMMENT '工单编号',
    `work_order_type` ENUM('REPAIR', 'MAINTENANCE', 'REPLACEMENT', 'CALIBRATION') NOT NULL COMMENT '工单类型',
    `priority` ENUM('URGENT', 'HIGH', 'MEDIUM', 'LOW') DEFAULT 'MEDIUM' COMMENT '优先级',
    `title` VARCHAR(200) NOT NULL COMMENT '工单标题',
    `description` TEXT COMMENT '工单描述',
    `estimated_duration` INT COMMENT '预计耗时(分钟)',
    `estimated_cost` DECIMAL(10,2) COMMENT '预计费用',
    `assigned_team` VARCHAR(100) COMMENT '分配团队',
    `assigned_technician` VARCHAR(50) COMMENT '分配技师',
    `required_parts` JSON COMMENT '所需配件',
    `required_tools` JSON COMMENT '所需工具',
    `status` ENUM('CREATED', 'ASSIGNED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED') DEFAULT 'CREATED' COMMENT '状态',
    `created_by` VARCHAR(50) COMMENT '创建人',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by` VARCHAR(50) COMMENT '更新人',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `tenant_id` VARCHAR(50) COMMENT '租户ID',
    `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_work_order_number` (`work_order_number`, `tenant_id`),
    INDEX `idx_exception_record` (`exception_record_id`),
    INDEX `idx_work_order_type` (`work_order_type`),
    INDEX `idx_priority` (`priority`),
    INDEX `idx_status` (`status`),
    INDEX `idx_tenant` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='自动工单表';

-- 插入默认异常处理规则
INSERT INTO `exception_handling_rules` (`id`, `rule_name`, `equipment_type`, `exception_keywords`, `exception_level`, `auto_actions`, `notification_rules`, `escalation_rules`, `priority`, `is_enabled`, `created_by`, `tenant_id`) VALUES
('RULE_001', '生命支持设备一级异常', 'LIFE_SUPPORT', '["报警", "停机", "故障", "无法启动", "异常停止"]', 'LEVEL_1', '{"auto_notify": true, "auto_work_order": true, "auto_escalate": true}', '{"immediate": ["SMS", "APP_PUSH", "PHONE_CALL"], "targets": ["设备科", "科室主任", "值班经理"]}', '{"time_limit": 5, "escalate_to": "医院管理层"}', 100, 1, 'SYSTEM', 'DEFAULT'),
('RULE_002', '监护设备二级异常', 'MONITORING', '["参数异常", "连接异常", "信号丢失"]', 'LEVEL_2', '{"auto_notify": true, "auto_work_order": true}', '{"immediate": ["SMS", "APP_PUSH"], "targets": ["设备科", "科室负责人"]}', '{"time_limit": 30, "escalate_to": "科室主任"}', 90, 1, 'SYSTEM', 'DEFAULT'),
('RULE_003', '治疗设备三级异常', 'THERAPEUTIC', '["性能下降", "精度偏差", "运行异常"]', 'LEVEL_3', '{"auto_notify": true, "auto_work_order": false}', '{"immediate": ["APP_PUSH"], "targets": ["设备科"]}', '{"time_limit": 120, "escalate_to": "设备科主任"}', 80, 1, 'SYSTEM', 'DEFAULT'),
('RULE_004', '一般设备四级异常', 'GENERAL', '["轻微异常", "提醒", "建议检查"]', 'LEVEL_4', '{"auto_notify": false, "auto_work_order": false}', '{"daily_report": true, "targets": ["设备科"]}', '{"time_limit": 1440, "escalate_to": "设备科"}', 70, 1, 'SYSTEM', 'DEFAULT');