-- 设备维修功能相关表结构

-- 设备维修工单表
CREATE TABLE IF NOT EXISTS `eq_repair_order` (
  `order_id` varchar(32) NOT NULL COMMENT '维修工单ID',
  `order_number` varchar(50) DEFAULT NULL COMMENT '工单编号',
  `order_title` varchar(200) NOT NULL COMMENT '工单标题',
  `equipment_id` varchar(32) NOT NULL COMMENT '设备ID',
  `equipment_name` varchar(100) NOT NULL COMMENT '设备名称',
  `equipment_code` varchar(50) DEFAULT NULL COMMENT '设备编码',
  `equipment_model` varchar(100) DEFAULT NULL COMMENT '设备型号',
  `equipment_location` varchar(200) DEFAULT NULL COMMENT '设备位置',
  `repair_type` varchar(30) DEFAULT 'FAULT_REPAIR' COMMENT '维修类型(FAULT_REPAIR-故障维修,PREVENTIVE_REPAIR-预防性维修,EMERGENCY_REPAIR-紧急维修,SCHEDULED_REPAIR-计划维修,MODIFICATION_REPAIR-改造维修,OVERHAUL-大修,MEDIUM_REPAIR-中修,MINOR_REPAIR-小修)',
  `fault_description` longtext COMMENT '故障现象',
  `fault_reason` longtext COMMENT '故障原因',
  `repair_content` longtext COMMENT '维修内容',
  `repair_plan` longtext COMMENT '维修方案',
  `order_status` varchar(30) DEFAULT 'PENDING_REPORT' COMMENT '工单状态(PENDING_REPORT-待报修,REPORTED-已报修,PENDING_ASSIGN-待分配,ASSIGNED-已分配,PENDING_CONFIRM-待确认,CONFIRMED-已确认,IN_REPAIR-维修中,PENDING_ACCEPTANCE-待验收,ACCEPTED-验收通过,REJECTED-验收不通过,COMPLETED-已完成,CANCELLED-已取消,CLOSED-已关闭)',
  `priority` int DEFAULT 2 COMMENT '优先级(1-低,2-中,3-高,4-紧急,5-特急)',
  `urgency_level` int DEFAULT 1 COMMENT '紧急程度(1-不紧急,2-一般,3-紧急,4-非常紧急)',
  `reporter_id` varchar(32) DEFAULT NULL COMMENT '报修人ID',
  `reporter_name` varchar(50) DEFAULT NULL COMMENT '报修人姓名',
  `report_dept_id` varchar(32) DEFAULT NULL COMMENT '报修部门ID',
  `report_dept_name` varchar(100) DEFAULT NULL COMMENT '报修部门名称',
  `report_time` datetime DEFAULT NULL COMMENT '报修时间',
  `assigned_by` varchar(32) DEFAULT NULL COMMENT '分配人ID',
  `assigned_by_name` varchar(50) DEFAULT NULL COMMENT '分配人姓名',
  `assigned_time` datetime DEFAULT NULL COMMENT '分配时间',
  `repair_person_id` varchar(32) DEFAULT NULL COMMENT '维修人员ID',
  `repair_person_name` varchar(50) DEFAULT NULL COMMENT '维修人员姓名',
  `repair_dept_id` varchar(32) DEFAULT NULL COMMENT '维修部门ID',
  `repair_dept_name` varchar(100) DEFAULT NULL COMMENT '维修部门名称',
  `accept_time` datetime DEFAULT NULL COMMENT '接单时间',
  `start_repair_time` datetime DEFAULT NULL COMMENT '开始维修时间',
  `finish_repair_time` datetime DEFAULT NULL COMMENT '完成维修时间',
  `planned_start_time` datetime DEFAULT NULL COMMENT '计划开始时间',
  `planned_end_time` datetime DEFAULT NULL COMMENT '计划结束时间',
  `actual_start_time` datetime DEFAULT NULL COMMENT '实际开始时间',
  `actual_end_time` datetime DEFAULT NULL COMMENT '实际结束时间',
  `estimated_hours` decimal(10,2) DEFAULT 0.00 COMMENT '预计工时(小时)',
  `actual_hours` decimal(10,2) DEFAULT 0.00 COMMENT '实际工时(小时)',
  `estimated_cost` decimal(10,2) DEFAULT 0.00 COMMENT '预计费用',
  `actual_cost` decimal(10,2) DEFAULT 0.00 COMMENT '实际费用',
  `material_cost` decimal(10,2) DEFAULT 0.00 COMMENT '材料费用',
  `labor_cost` decimal(10,2) DEFAULT 0.00 COMMENT '人工费用',
  `other_cost` decimal(10,2) DEFAULT 0.00 COMMENT '其他费用',
  `repair_result` longtext COMMENT '维修结果',
  `repair_summary` longtext COMMENT '维修总结',
  `replaced_parts` longtext COMMENT '更换部件',
  `used_materials` longtext COMMENT '使用材料',
  `acceptor_id` varchar(32) DEFAULT NULL COMMENT '验收人ID',
  `acceptor_name` varchar(50) DEFAULT NULL COMMENT '验收人姓名',
  `acceptance_time` datetime DEFAULT NULL COMMENT '验收时间',
  `acceptance_result` varchar(20) DEFAULT NULL COMMENT '验收结果(PASS-通过,FAIL-不通过)',
  `acceptance_comments` longtext COMMENT '验收意见',
  `closed_by` varchar(32) DEFAULT NULL COMMENT '关闭人ID',
  `closed_by_name` varchar(50) DEFAULT NULL COMMENT '关闭人姓名',
  `closed_time` datetime DEFAULT NULL COMMENT '关闭时间',
  `close_reason` longtext COMMENT '关闭原因',
  `satisfaction_score` int DEFAULT NULL COMMENT '满意度评分(1-5分)',
  `satisfaction_comments` longtext COMMENT '满意度评价',
  `is_outsourced` int DEFAULT 0 COMMENT '是否外包(0-否,1-是)',
  `outsourced_vendor` varchar(100) DEFAULT NULL COMMENT '外包供应商',
  `outsourced_contact` varchar(50) DEFAULT NULL COMMENT '外包联系人',
  `outsourced_phone` varchar(20) DEFAULT NULL COMMENT '外包联系电话',
  `is_emergency` int DEFAULT 0 COMMENT '是否紧急(0-否,1-是)',
  `is_shutdown` int DEFAULT 0 COMMENT '是否停机(0-否,1-是)',
  `shutdown_start_time` datetime DEFAULT NULL COMMENT '停机开始时间',
  `shutdown_end_time` datetime DEFAULT NULL COMMENT '停机结束时间',
  `shutdown_duration` decimal(10,2) DEFAULT 0.00 COMMENT '停机时长(小时)',
  `impact_scope` varchar(500) DEFAULT NULL COMMENT '影响范围',
  `impact_level` int DEFAULT 1 COMMENT '影响程度(1-轻微,2-一般,3-严重,4-重大)',
  `photo_attachments` longtext COMMENT '照片附件',
  `document_attachments` longtext COMMENT '文档附件',
  `video_attachments` longtext COMMENT '视频附件',
  `remarks` longtext COMMENT '备注',
  `ext1` varchar(255) DEFAULT NULL COMMENT '扩展字段1',
  `ext2` varchar(255) DEFAULT NULL COMMENT '扩展字段2',
  `ext3` varchar(255) DEFAULT NULL COMMENT '扩展字段3',
  `ext4` varchar(255) DEFAULT NULL COMMENT '扩展字段4',
  `ext5` varchar(255) DEFAULT NULL COMMENT '扩展字段5',
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人ID',
  `is_deleted` int DEFAULT 0 COMMENT '逻辑删除(0-未删除,1-已删除)',
  PRIMARY KEY (`order_id`),
  UNIQUE KEY `uk_order_number` (`order_number`),
  KEY `idx_equipment_id` (`equipment_id`),
  KEY `idx_repair_type` (`repair_type`),
  KEY `idx_order_status` (`order_status`),
  KEY `idx_priority` (`priority`),
  KEY `idx_reporter_id` (`reporter_id`),
  KEY `idx_repair_person_id` (`repair_person_id`),
  KEY `idx_repair_dept_id` (`repair_dept_id`),
  KEY `idx_report_time` (`report_time`),
  KEY `idx_is_emergency` (`is_emergency`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='设备维修工单表';

-- 设备维修记录表
CREATE TABLE IF NOT EXISTS `eq_repair_record` (
  `record_id` varchar(32) NOT NULL COMMENT '维修记录ID',
  `record_number` varchar(50) DEFAULT NULL COMMENT '记录编号',
  `order_id` varchar(32) DEFAULT NULL COMMENT '维修工单ID',
  `order_number` varchar(50) DEFAULT NULL COMMENT '工单编号',
  `equipment_id` varchar(32) NOT NULL COMMENT '设备ID',
  `equipment_name` varchar(100) NOT NULL COMMENT '设备名称',
  `equipment_code` varchar(50) DEFAULT NULL COMMENT '设备编码',
  `repair_type` varchar(30) DEFAULT 'FAULT_REPAIR' COMMENT '维修类型(FAULT_REPAIR-故障维修,PREVENTIVE_REPAIR-预防性维修,EMERGENCY_REPAIR-紧急维修,SCHEDULED_REPAIR-计划维修,MODIFICATION_REPAIR-改造维修,OVERHAUL-大修,MEDIUM_REPAIR-中修,MINOR_REPAIR-小修)',
  `fault_description` longtext COMMENT '故障现象',
  `fault_reason` longtext COMMENT '故障原因',
  `fault_analysis` longtext COMMENT '故障分析',
  `before_repair_status` longtext COMMENT '维修前状态',
  `after_repair_status` longtext COMMENT '维修后状态',
  `repair_content` longtext COMMENT '维修内容',
  `repair_method` longtext COMMENT '维修方法',
  `repair_process` longtext COMMENT '维修过程',
  `repair_result` longtext COMMENT '维修结果',
  `repair_summary` longtext COMMENT '维修总结',
  `repair_person_id` varchar(32) DEFAULT NULL COMMENT '维修人员ID',
  `repair_person_name` varchar(50) DEFAULT NULL COMMENT '维修人员姓名',
  `repair_dept_id` varchar(32) DEFAULT NULL COMMENT '维修部门ID',
  `repair_dept_name` varchar(100) DEFAULT NULL COMMENT '维修部门名称',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `repair_hours` decimal(10,2) DEFAULT 0.00 COMMENT '维修工时(小时)',
  `downtime_hours` decimal(10,2) DEFAULT 0.00 COMMENT '停机时间(小时)',
  `replaced_parts` longtext COMMENT '更换部件',
  `used_materials` longtext COMMENT '使用材料',
  `used_tools` longtext COMMENT '使用工具',
  `material_cost` decimal(10,2) DEFAULT 0.00 COMMENT '材料费用',
  `labor_cost` decimal(10,2) DEFAULT 0.00 COMMENT '人工费用',
  `other_cost` decimal(10,2) DEFAULT 0.00 COMMENT '其他费用',
  `total_cost` decimal(10,2) DEFAULT 0.00 COMMENT '总费用',
  `quality_evaluation` longtext COMMENT '质量评价',
  `evaluated_by` varchar(32) DEFAULT NULL COMMENT '评价人ID',
  `evaluated_by_name` varchar(50) DEFAULT NULL COMMENT '评价人姓名',
  `evaluated_time` datetime DEFAULT NULL COMMENT '评价时间',
  `evaluation_score` decimal(5,2) DEFAULT NULL COMMENT '评价得分',
  `is_rework` int DEFAULT 0 COMMENT '是否返工(0-否,1-是)',
  `rework_reason` longtext COMMENT '返工原因',
  `rework_count` int DEFAULT 0 COMMENT '返工次数',
  `is_outsourced` int DEFAULT 0 COMMENT '是否外包(0-否,1-是)',
  `outsourced_vendor` varchar(100) DEFAULT NULL COMMENT '外包供应商',
  `warranty_period` int DEFAULT 0 COMMENT '保修期(月)',
  `warranty_expiry` datetime DEFAULT NULL COMMENT '保修到期时间',
  `prevention_measures` longtext COMMENT '预防措施',
  `improvement_suggestions` longtext COMMENT '改进建议',
  `photo_attachments` longtext COMMENT '照片附件',
  `document_attachments` longtext COMMENT '文档附件',
  `video_attachments` longtext COMMENT '视频附件',
  `audio_attachments` longtext COMMENT '音频附件',
  `weather_condition` varchar(50) DEFAULT NULL COMMENT '天气情况',
  `environment_temperature` decimal(5,2) DEFAULT NULL COMMENT '环境温度',
  `environment_humidity` decimal(5,2) DEFAULT NULL COMMENT '环境湿度',
  `remarks` longtext COMMENT '备注',
  `ext1` varchar(255) DEFAULT NULL COMMENT '扩展字段1',
  `ext2` varchar(255) DEFAULT NULL COMMENT '扩展字段2',
  `ext3` varchar(255) DEFAULT NULL COMMENT '扩展字段3',
  `ext4` varchar(255) DEFAULT NULL COMMENT '扩展字段4',
  `ext5` varchar(255) DEFAULT NULL COMMENT '扩展字段5',
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人ID',
  `is_deleted` int DEFAULT 0 COMMENT '逻辑删除(0-未删除,1-已删除)',
  PRIMARY KEY (`record_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_equipment_id` (`equipment_id`),
  KEY `idx_repair_type` (`repair_type`),
  KEY `idx_repair_person_id` (`repair_person_id`),
  KEY `idx_repair_dept_id` (`repair_dept_id`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_end_time` (`end_time`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='设备维修记录表';

-- 设备维修模板表
CREATE TABLE IF NOT EXISTS `eq_repair_template` (
  `template_id` varchar(32) NOT NULL COMMENT '维修模板ID',
  `template_name` varchar(100) NOT NULL COMMENT '模板名称',
  `template_code` varchar(50) DEFAULT NULL COMMENT '模板编码',
  `equipment_type_id` varchar(32) DEFAULT NULL COMMENT '设备类型ID',
  `equipment_type_name` varchar(100) DEFAULT NULL COMMENT '设备类型名称',
  `equipment_model` varchar(100) DEFAULT NULL COMMENT '设备型号',
  `repair_type` varchar(30) DEFAULT 'FAULT_REPAIR' COMMENT '维修类型(FAULT_REPAIR-故障维修,PREVENTIVE_REPAIR-预防性维修,EMERGENCY_REPAIR-紧急维修,SCHEDULED_REPAIR-计划维修,MODIFICATION_REPAIR-改造维修,OVERHAUL-大修,MEDIUM_REPAIR-中修,MINOR_REPAIR-小修)',
  `fault_type` varchar(100) DEFAULT NULL COMMENT '故障类型',
  `fault_description` longtext COMMENT '故障现象',
  `fault_reason` longtext COMMENT '故障原因',
  `repair_content` longtext COMMENT '维修内容',
  `repair_method` longtext COMMENT '维修方法',
  `repair_steps` longtext COMMENT '维修步骤',
  `repair_standard` longtext COMMENT '维修标准',
  `estimated_hours` decimal(10,2) DEFAULT 0.00 COMMENT '预计工时(小时)',
  `estimated_cost` decimal(10,2) DEFAULT 0.00 COMMENT '预计费用',
  `required_skills` longtext COMMENT '所需技能',
  `required_tools` longtext COMMENT '所需工具',
  `required_materials` longtext COMMENT '所需材料',
  `required_parts` longtext COMMENT '所需配件',
  `safety_precautions` longtext COMMENT '安全注意事项',
  `quality_standards` longtext COMMENT '质量标准',
  `acceptance_criteria` longtext COMMENT '验收标准',
  `prevention_measures` longtext COMMENT '预防措施',
  `create_dept_id` varchar(32) DEFAULT NULL COMMENT '创建部门ID',
  `create_dept_name` varchar(100) DEFAULT NULL COMMENT '创建部门名称',
  `reviewed_by` varchar(32) DEFAULT NULL COMMENT '审核人ID',
  `reviewed_by_name` varchar(50) DEFAULT NULL COMMENT '审核人姓名',
  `reviewed_time` datetime DEFAULT NULL COMMENT '审核时间',
  `review_status` int DEFAULT 0 COMMENT '审核状态(0-待审核,1-已审核,2-已拒绝)',
  `review_comments` longtext COMMENT '审核意见',
  `template_status` int DEFAULT 0 COMMENT '模板状态(0-草稿,1-生效,2-停用)',
  `version` varchar(20) DEFAULT '1.0' COMMENT '版本号',
  `effective_date` datetime DEFAULT NULL COMMENT '生效日期',
  `expiry_date` datetime DEFAULT NULL COMMENT '失效日期',
  `usage_count` int DEFAULT 0 COMMENT '使用次数',
  `attachments` longtext COMMENT '附件',
  `remarks` longtext COMMENT '备注',
  `is_default` int DEFAULT 0 COMMENT '是否默认(0-否,1-是)',
  `is_public` int DEFAULT 0 COMMENT '是否公开(0-否,1-是)',
  `sort_order` int DEFAULT 0 COMMENT '排序',
  `ext1` varchar(255) DEFAULT NULL COMMENT '扩展字段1',
  `ext2` varchar(255) DEFAULT NULL COMMENT '扩展字段2',
  `ext3` varchar(255) DEFAULT NULL COMMENT '扩展字段3',
  `ext4` varchar(255) DEFAULT NULL COMMENT '扩展字段4',
  `ext5` varchar(255) DEFAULT NULL COMMENT '扩展字段5',
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人ID',
  `is_deleted` int DEFAULT 0 COMMENT '逻辑删除(0-未删除,1-已删除)',
  PRIMARY KEY (`template_id`),
  KEY `idx_equipment_type_id` (`equipment_type_id`),
  KEY `idx_repair_type` (`repair_type`),
  KEY `idx_fault_type` (`fault_type`),
  KEY `idx_template_status` (`template_status`),
  KEY `idx_review_status` (`review_status`),
  KEY `idx_is_default` (`is_default`),
  KEY `idx_is_public` (`is_public`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='设备维修模板表';

-- 插入测试数据

-- 维修模板测试数据
INSERT INTO `eq_repair_template` (`template_id`, `template_name`, `template_code`, `equipment_type_id`, `equipment_type_name`, `equipment_model`, `repair_type`, `fault_type`, `fault_description`, `fault_reason`, `repair_content`, `repair_method`, `repair_steps`, `repair_standard`, `estimated_hours`, `estimated_cost`, `required_skills`, `required_tools`, `required_materials`, `required_parts`, `safety_precautions`, `quality_standards`, `acceptance_criteria`, `prevention_measures`, `template_status`, `version`, `usage_count`, `is_default`, `is_public`, `sort_order`, `tenant_id`, `create_by`) VALUES
('rt_001', '空调制冷故障维修模板', 'HVAC_COOLING_001', 'et_001', '空调设备', 'VRV系列', 'FAULT_REPAIR', '制冷故障', '空调不制冷或制冷效果差', '可能原因：1.制冷剂不足 2.过滤器堵塞 3.压缩机故障 4.冷凝器脏污', '1.检查制冷剂压力\n2.清洗过滤器和冷凝器\n3.检查压缩机工作状态\n4.检查电气连接\n5.添加制冷剂或更换故障部件', '故障诊断→部件检查→清洗保养→部件更换→功能测试', '1.关闭电源并挂牌\n2.检查制冷剂压力表读数\n3.拆卸并清洗过滤器\n4.清洗冷凝器翅片\n5.检查压缩机运行电流\n6.检查电气接线\n7.添加制冷剂至标准压力\n8.试运行并测试制冷效果', '制冷效果达到设计要求，运行噪音正常，无泄漏', 4.00, 300.00, '制冷设备维修技能、电工基础知识', '压力表、清洗设备、万用表、制冷剂回收设备', '制冷剂、清洁剂、润滑油', '过滤器、密封圈、电气元件', '断电操作、制冷剂处理安全、防触电', '制冷效果正常、无泄漏、运行稳定', '制冷量达标、压力正常、电流正常', '定期清洗过滤器和冷凝器，检查制冷剂压力', 1, '1.0', 0, 1, 1, 1, 'tenant_001', 'user_001'),
('rt_002', '电梯困人故障维修模板', 'ELEVATOR_TRAP_001', 'et_002', '电梯设备', 'OTIS系列', 'EMERGENCY_REPAIR', '困人故障', '电梯停止运行，乘客被困轿厢内', '可能原因：1.停电 2.安全回路断开 3.门锁故障 4.控制系统故障', '1.确认故障现象\n2.安抚被困人员\n3.检查电源和安全回路\n4.手动救援操作\n5.修复故障原因', '应急救援→故障诊断→部件修复→安全测试', '1.立即响应救援\n2.与被困人员通话安抚\n3.检查机房电源状态\n4.检查安全回路\n5.实施手动救援\n6.修复故障部件\n7.恢复正常运行\n8.安全功能测试', '救援及时、修复彻底、安全可靠', 2.00, 150.00, '电梯维修技能、应急救援技能', '专用工具、应急照明、通讯设备', '备用配件、润滑剂', '门锁装置、安全开关、控制元件', '高空作业安全、电气安全、应急救援安全', '救援及时、修复彻底、功能正常', '救援时间≤30分钟、安全测试通过', '定期检查安全回路和门锁系统', 1, '1.0', 0, 1, 1, 2, 'tenant_001', 'user_001'),
('rt_003', '消防泵启动故障维修模板', 'FIRE_PUMP_001', 'et_003', '消防设备', '通用型', 'EMERGENCY_REPAIR', '启动故障', '消防泵无法启动或启动后立即停止', '可能原因：1.电源故障 2.控制回路故障 3.泵体堵塞 4.机械故障', '1.检查电源和控制回路\n2.检查泵体和管路\n3.检查机械传动部分\n4.更换故障部件\n5.系统测试', '电气检查→机械检查→部件更换→系统测试', '1.检查电源电压和相序\n2.检查控制柜接线\n3.检查水泵吸水管路\n4.检查叶轮和轴承\n5.更换故障部件\n6.手动/自动启动测试\n7.压力和流量测试', '启动正常、压力流量达标、自动控制正常', 3.00, 200.00, '消防设备维修技能、电气维修技能', '万用表、扳手、起重设备', '轴承、密封件、润滑油', '控制元件、机械部件', '电气安全、防火安全、设备安全', '启动可靠、压力流量符合要求', '启动时间≤30秒、压力流量达标', '定期检查控制系统和机械部件', 1, '1.0', 0, 1, 1, 3, 'tenant_001', 'user_001');

-- 维修工单测试数据
INSERT INTO `eq_repair_order` (`order_id`, `order_number`, `order_title`, `equipment_id`, `equipment_name`, `equipment_code`, `equipment_model`, `equipment_location`, `repair_type`, `fault_description`, `fault_reason`, `repair_content`, `order_status`, `priority`, `urgency_level`, `reporter_id`, `reporter_name`, `report_dept_id`, `report_dept_name`, `report_time`, `repair_person_id`, `repair_person_name`, `repair_dept_id`, `repair_dept_name`, `planned_start_time`, `planned_end_time`, `estimated_hours`, `estimated_cost`, `is_emergency`, `is_shutdown`, `impact_level`, `tenant_id`, `create_by`) VALUES
('ro_001', 'RO_20241207_001', '1号楼空调制冷故障', 'eq_001', '1号楼中央空调', 'HVAC_001', 'VRV系列', '1号楼机房', 'FAULT_REPAIR', '空调不制冷，室内温度偏高', '初步判断为制冷剂不足或过滤器堵塞', '检查制冷系统，清洗过滤器，补充制冷剂', 'ASSIGNED', 3, 3, 'user_001', '张三', 'dept_001', '行政部', '2024-12-07 09:00:00', 'user_002', '李四', 'dept_002', '设备部', '2024-12-07 14:00:00', '2024-12-07 18:00:00', 4.00, 300.00, 0, 0, 2, 'tenant_001', 'user_001'),
('ro_002', 'RO_20241207_002', '办公楼电梯异响', 'eq_002', '办公楼客梯', 'ELEVATOR_001', 'OTIS系列', '办公楼1-10层', 'FAULT_REPAIR', '电梯运行时有异响，门开关不畅', '可能是门机构需要润滑调整', '检查门机构，调整门系统，添加润滑剂', 'REPORTED', 2, 2, 'user_003', '王五', 'dept_003', '办公室', '2024-12-07 10:30:00', NULL, NULL, NULL, NULL, '2024-12-08 08:00:00', '2024-12-08 12:00:00', 3.00, 150.00, 0, 0, 1, 'tenant_001', 'user_003'),
('ro_003', 'RO_20241207_003', '消防泵房设备检修', 'eq_003', '消防泵房设备', 'FIRE_001', '通用型', '地下一层消防泵房', 'SCHEDULED_REPAIR', '按计划进行消防设备检修保养', '定期维护保养', '全面检查消防泵、控制系统、管路系统', 'PENDING_ASSIGN', 4, 4, 'user_004', '赵六', 'dept_004', '安保部', '2024-12-07 11:00:00', NULL, NULL, NULL, NULL, '2024-12-08 09:00:00', '2024-12-08 17:00:00', 8.00, 500.00, 1, 1, 3, 'tenant_001', 'user_004');

-- 维修记录测试数据
INSERT INTO `eq_repair_record` (`record_id`, `record_number`, `order_id`, `order_number`, `equipment_id`, `equipment_name`, `equipment_code`, `repair_type`, `fault_description`, `fault_reason`, `fault_analysis`, `before_repair_status`, `after_repair_status`, `repair_content`, `repair_method`, `repair_process`, `repair_result`, `repair_summary`, `repair_person_id`, `repair_person_name`, `repair_dept_id`, `repair_dept_name`, `start_time`, `end_time`, `repair_hours`, `downtime_hours`, `replaced_parts`, `used_materials`, `material_cost`, `labor_cost`, `total_cost`, `quality_evaluation`, `evaluation_score`, `is_rework`, `warranty_period`, `tenant_id`, `create_by`) VALUES
('rr_001', 'RR_20241206_001', 'ro_001', 'RO_20241206_001', 'eq_001', '1号楼中央空调', 'HVAC_001', 'FAULT_REPAIR', '空调不制冷，室内温度偏高', '制冷剂不足，过滤器堵塞', '检查发现制冷剂压力低于正常值，过滤器严重堵塞，影响制冷效果', '制冷效果差，室内温度28℃', '制冷正常，室内温度22℃', '清洗过滤器，补充制冷剂，检查管路密封', '拆卸清洗过滤器，使用专用设备补充制冷剂', '1.关闭电源\n2.拆卸过滤器进行清洗\n3.检查制冷剂压力\n4.补充制冷剂至标准压力\n5.检查管路密封性\n6.试运行测试', '维修成功，制冷效果恢复正常', '通过清洗过滤器和补充制冷剂，空调制冷功能恢复正常，室内温度达到设定值', 'user_002', '李四', 'dept_002', '设备部', '2024-12-06 14:00:00', '2024-12-06 18:00:00', 4.00, 0.50, '过滤器', '制冷剂2kg，清洁剂1瓶', 120.00, 200.00, 320.00, '维修质量良好，设备运行正常', 95.00, 0, 6, 'tenant_001', 'user_002'),
('rr_002', 'RR_20241205_001', 'ro_002', 'RO_20241205_001', 'eq_002', '办公楼客梯', 'ELEVATOR_001', 'FAULT_REPAIR', '电梯运行时有异响，门开关不畅', '门导轨缺乏润滑，门机构调整不当', '检查发现门导轨干涩，门机构参数偏移，导致开关门不畅并产生异响', '门开关有阻滞和异响', '门开关顺畅，无异响', '调整门机构参数，清洁润滑门导轨', '拆卸检查门机构，清洁润滑导轨，调整相关参数', '1.停用电梯\n2.拆卸门机构外罩\n3.清洁门导轨\n4.添加专用润滑剂\n5.调整门机构参数\n6.反复测试门开关\n7.恢复正常运行', '维修成功，门系统运行正常', '通过清洁润滑和参数调整，电梯门系统恢复正常运行，异响消除', 'user_005', '钱七', 'dept_002', '设备部', '2024-12-05 08:00:00', '2024-12-05 11:00:00', 3.00, 2.00, '门锁开关', '润滑剂，清洁剂', 80.00, 150.00, 230.00, '维修质量优秀，运行平稳', 98.00, 0, 12, 'tenant_001', 'user_005'),
('rr_003', 'RR_20241204_001', 'ro_003', 'RO_20241204_001', 'eq_003', '消防泵房设备', 'FIRE_001', 'SCHEDULED_REPAIR', '按计划进行消防设备检修保养', '定期维护保养', '全面检修保养，发现部分密封件老化，控制回路接触不良', '设备运行正常，部分部件老化', '设备状态良好，各项指标正常', '更换老化密封件，处理控制回路，全面清洁保养', '拆检主要部件，更换老化件，测试各项功能', '1.关闭消防泵\n2.拆检泵体和控制柜\n3.更换老化密封件\n4.清洁处理控制回路\n5.重新组装调试\n6.压力和流量测试\n7.自动控制测试', '维修保养完成，设备状态良好', '通过全面检修保养，消防泵设备状态良好，各项功能正常，可靠性提高', 'user_006', '孙八', 'dept_002', '设备部', '2024-12-04 09:00:00', '2024-12-04 17:00:00', 8.00, 6.00, '密封件3套，接触器1个', '密封胶，清洁剂，润滑剂', 200.00, 400.00, 600.00, '维修保养质量良好，设备可靠', 96.00, 0, 12, 'tenant_001', 'user_006');