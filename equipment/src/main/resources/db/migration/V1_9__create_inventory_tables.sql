-- 设备盘点功能相关表结构

-- 设备盘点计划表
CREATE TABLE IF NOT EXISTS `eq_inventory_plan` (
  `plan_id` varchar(32) NOT NULL COMMENT '盘点计划ID',
  `plan_name` varchar(100) NOT NULL COMMENT '计划名称',
  `plan_number` varchar(50) DEFAULT NULL COMMENT '计划编号',
  `inventory_type` varchar(30) DEFAULT 'REGULAR_INVENTORY' COMMENT '盘点类型(FULL_INVENTORY-全面盘点,KEY_INVENTORY-重点盘点,SAMPLE_INVENTORY-抽样盘点,REGULAR_INVENTORY-定期盘点,SPECIAL_INVENTORY-专项盘点,EMERGENCY_INVENTORY-应急盘点,DEPARTMENT_INVENTORY-部门盘点,AREA_INVENTORY-区域盘点)',
  `inventory_scope` longtext COMMENT '盘点范围',
  `inventory_purpose` longtext COMMENT '盘点目的',
  `inventory_description` longtext COMMENT '盘点说明',
  `plan_status` varchar(30) DEFAULT 'PLANNED' COMMENT '计划状态(PLANNED-计划中,PREPARING-准备中,IN_PROGRESS-进行中,PAUSED-已暂停,PENDING_REVIEW-待复盘,REVIEWING-复盘中,PENDING_ADJUSTMENT-待调整,ADJUSTING-调整中,PENDING_CONFIRM-待确认,COMPLETED-已完成,CANCELLED-已取消,CLOSED-已关闭)',
  `planned_start_time` datetime DEFAULT NULL COMMENT '计划开始时间',
  `planned_end_time` datetime DEFAULT NULL COMMENT '计划结束时间',
  `actual_start_time` datetime DEFAULT NULL COMMENT '实际开始时间',
  `actual_end_time` datetime DEFAULT NULL COMMENT '实际结束时间',
  `supervisor_id` varchar(32) DEFAULT NULL COMMENT '盘点负责人ID',
  `supervisor_name` varchar(50) DEFAULT NULL COMMENT '盘点负责人姓名',
  `organize_dept_id` varchar(32) DEFAULT NULL COMMENT '组织部门ID',
  `organize_dept_name` varchar(100) DEFAULT NULL COMMENT '组织部门名称',
  `participate_depts` longtext COMMENT '参与部门',
  `inventory_personnel` longtext COMMENT '盘点人员',
  `estimated_equipment_count` int DEFAULT 0 COMMENT '预计盘点设备数',
  `actual_equipment_count` int DEFAULT 0 COMMENT '实际盘点设备数',
  `completed_count` int DEFAULT 0 COMMENT '盘点完成数',
  `progress_percentage` decimal(5,2) DEFAULT 0.00 COMMENT '盘点进度(%)',
  `normal_count` int DEFAULT 0 COMMENT '正常数量',
  `abnormal_count` int DEFAULT 0 COMMENT '异常数量',
  `surplus_count` int DEFAULT 0 COMMENT '盈余数量',
  `shortage_count` int DEFAULT 0 COMMENT '亏损数量',
  `damaged_count` int DEFAULT 0 COMMENT '损坏数量',
  `lost_count` int DEFAULT 0 COMMENT '丢失数量',
  `estimated_total_value` decimal(15,2) DEFAULT 0.00 COMMENT '预计总价值',
  `actual_total_value` decimal(15,2) DEFAULT 0.00 COMMENT '实际总价值',
  `value_difference` decimal(15,2) DEFAULT 0.00 COMMENT '价值差异',
  `inventory_requirements` longtext COMMENT '盘点要求',
  `notice_items` longtext COMMENT '注意事项',
  `approver_id` varchar(32) DEFAULT NULL COMMENT '审批人ID',
  `approver_name` varchar(50) DEFAULT NULL COMMENT '审批人姓名',
  `approved_time` datetime DEFAULT NULL COMMENT '审批时间',
  `approval_comments` longtext COMMENT '审批意见',
  `reviewer_id` varchar(32) DEFAULT NULL COMMENT '复盘人ID',
  `reviewer_name` varchar(50) DEFAULT NULL COMMENT '复盘人姓名',
  `reviewed_time` datetime DEFAULT NULL COMMENT '复盘时间',
  `review_result` varchar(20) DEFAULT NULL COMMENT '复盘结果',
  `review_comments` longtext COMMENT '复盘意见',
  `confirmer_id` varchar(32) DEFAULT NULL COMMENT '确认人ID',
  `confirmer_name` varchar(50) DEFAULT NULL COMMENT '确认人姓名',
  `confirmed_time` datetime DEFAULT NULL COMMENT '确认时间',
  `confirm_comments` longtext COMMENT '确认意见',
  `attachments` longtext COMMENT '附件',
  `remarks` longtext COMMENT '备注',
  `is_urgent` int DEFAULT 0 COMMENT '是否紧急(0-否,1-是)',
  `priority` int DEFAULT 2 COMMENT '优先级(1-低,2-中,3-高,4-紧急)',
  `ext1` varchar(255) DEFAULT NULL COMMENT '扩展字段1',
  `ext2` varchar(255) DEFAULT NULL COMMENT '扩展字段2',
  `ext3` varchar(255) DEFAULT NULL COMMENT '扩展字段3',
  `ext4` varchar(255) DEFAULT NULL COMMENT '扩展字段4',
  `ext5` varchar(255) DEFAULT NULL COMMENT '扩展字段5',
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人ID',
  `is_deleted` int DEFAULT 0 COMMENT '逻辑删除(0-未删除,1-已删除)',
  PRIMARY KEY (`plan_id`),
  UNIQUE KEY `uk_plan_number` (`plan_number`),
  KEY `idx_inventory_type` (`inventory_type`),
  KEY `idx_plan_status` (`plan_status`),
  KEY `idx_supervisor_id` (`supervisor_id`),
  KEY `idx_organize_dept_id` (`organize_dept_id`),
  KEY `idx_planned_start_time` (`planned_start_time`),
  KEY `idx_is_urgent` (`is_urgent`),
  KEY `idx_priority` (`priority`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='设备盘点计划表';

-- 设备盘点任务表
CREATE TABLE IF NOT EXISTS `eq_inventory_task` (
  `task_id` varchar(32) NOT NULL COMMENT '盘点任务ID',
  `task_number` varchar(50) DEFAULT NULL COMMENT '任务编号',
  `task_name` varchar(100) NOT NULL COMMENT '任务名称',
  `plan_id` varchar(32) NOT NULL COMMENT '盘点计划ID',
  `plan_name` varchar(100) DEFAULT NULL COMMENT '计划名称',
  `inventory_area` varchar(200) DEFAULT NULL COMMENT '盘点区域',
  `inventory_dept_id` varchar(32) DEFAULT NULL COMMENT '盘点部门ID',
  `inventory_dept_name` varchar(100) DEFAULT NULL COMMENT '盘点部门名称',
  `inventory_location` varchar(200) DEFAULT NULL COMMENT '盘点位置',
  `task_status` varchar(30) DEFAULT 'PLANNED' COMMENT '任务状态(PLANNED-计划中,ASSIGNED-已分配,IN_PROGRESS-进行中,PAUSED-已暂停,COMPLETED-已完成,CANCELLED-已取消)',
  `planned_start_time` datetime DEFAULT NULL COMMENT '计划开始时间',
  `planned_end_time` datetime DEFAULT NULL COMMENT '计划结束时间',
  `actual_start_time` datetime DEFAULT NULL COMMENT '实际开始时间',
  `actual_end_time` datetime DEFAULT NULL COMMENT '实际结束时间',
  `inventory_person_id` varchar(32) DEFAULT NULL COMMENT '盘点人员ID',
  `inventory_person_name` varchar(50) DEFAULT NULL COMMENT '盘点人员姓名',
  `assistant_personnel` longtext COMMENT '协助人员',
  `assigned_by` varchar(32) DEFAULT NULL COMMENT '分配人ID',
  `assigned_by_name` varchar(50) DEFAULT NULL COMMENT '分配人姓名',
  `assigned_time` datetime DEFAULT NULL COMMENT '分配时间',
  `estimated_equipment_count` int DEFAULT 0 COMMENT '预计盘点设备数',
  `actual_equipment_count` int DEFAULT 0 COMMENT '实际盘点设备数',
  `completed_count` int DEFAULT 0 COMMENT '盘点完成数',
  `progress_percentage` decimal(5,2) DEFAULT 0.00 COMMENT '盘点进度(%)',
  `normal_count` int DEFAULT 0 COMMENT '正常数量',
  `abnormal_count` int DEFAULT 0 COMMENT '异常数量',
  `surplus_count` int DEFAULT 0 COMMENT '盈余数量',
  `shortage_count` int DEFAULT 0 COMMENT '亏损数量',
  `damaged_count` int DEFAULT 0 COMMENT '损坏数量',
  `lost_count` int DEFAULT 0 COMMENT '丢失数量',
  `estimated_total_value` decimal(15,2) DEFAULT 0.00 COMMENT '预计总价值',
  `actual_total_value` decimal(15,2) DEFAULT 0.00 COMMENT '实际总价值',
  `value_difference` decimal(15,2) DEFAULT 0.00 COMMENT '价值差异',
  `task_description` longtext COMMENT '任务说明',
  `inventory_requirements` longtext COMMENT '盘点要求',
  `completion_status` varchar(100) DEFAULT NULL COMMENT '完成情况',
  `completion_time` datetime DEFAULT NULL COMMENT '完成时间',
  `submit_time` datetime DEFAULT NULL COMMENT '提交时间',
  `reviewer_id` varchar(32) DEFAULT NULL COMMENT '审核人ID',
  `reviewer_name` varchar(50) DEFAULT NULL COMMENT '审核人姓名',
  `reviewed_time` datetime DEFAULT NULL COMMENT '审核时间',
  `review_result` varchar(20) DEFAULT NULL COMMENT '审核结果(PASS-通过,REJECT-驳回)',
  `review_comments` longtext COMMENT '审核意见',
  `problem_description` longtext COMMENT '问题描述',
  `handling_suggestion` longtext COMMENT '处理建议',
  `attachments` longtext COMMENT '附件',
  `remarks` longtext COMMENT '备注',
  `priority` int DEFAULT 2 COMMENT '优先级(1-低,2-中,3-高,4-紧急)',
  `ext1` varchar(255) DEFAULT NULL COMMENT '扩展字段1',
  `ext2` varchar(255) DEFAULT NULL COMMENT '扩展字段2',
  `ext3` varchar(255) DEFAULT NULL COMMENT '扩展字段3',
  `ext4` varchar(255) DEFAULT NULL COMMENT '扩展字段4',
  `ext5` varchar(255) DEFAULT NULL COMMENT '扩展字段5',
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人ID',
  `is_deleted` int DEFAULT 0 COMMENT '逻辑删除(0-未删除,1-已删除)',
  PRIMARY KEY (`task_id`),
  KEY `idx_plan_id` (`plan_id`),
  KEY `idx_task_status` (`task_status`),
  KEY `idx_inventory_person_id` (`inventory_person_id`),
  KEY `idx_inventory_dept_id` (`inventory_dept_id`),
  KEY `idx_planned_start_time` (`planned_start_time`),
  KEY `idx_priority` (`priority`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='设备盘点任务表';

-- 设备盘点记录表
CREATE TABLE IF NOT EXISTS `eq_inventory_record` (
  `record_id` varchar(32) NOT NULL COMMENT '盘点记录ID',
  `record_number` varchar(50) DEFAULT NULL COMMENT '记录编号',
  `plan_id` varchar(32) DEFAULT NULL COMMENT '盘点计划ID',
  `task_id` varchar(32) DEFAULT NULL COMMENT '盘点任务ID',
  `equipment_id` varchar(32) NOT NULL COMMENT '设备ID',
  `equipment_name` varchar(100) NOT NULL COMMENT '设备名称',
  `equipment_code` varchar(50) DEFAULT NULL COMMENT '设备编码',
  `equipment_model` varchar(100) DEFAULT NULL COMMENT '设备型号',
  `equipment_type_id` varchar(32) DEFAULT NULL COMMENT '设备类型ID',
  `equipment_type_name` varchar(100) DEFAULT NULL COMMENT '设备类型名称',
  `equipment_category_id` varchar(32) DEFAULT NULL COMMENT '设备分类ID',
  `equipment_category_name` varchar(100) DEFAULT NULL COMMENT '设备分类名称',
  `book_quantity` int DEFAULT 1 COMMENT '账面数量',
  `actual_quantity` int DEFAULT 0 COMMENT '实盘数量',
  `difference_quantity` int DEFAULT 0 COMMENT '差异数量',
  `book_value` decimal(15,2) DEFAULT 0.00 COMMENT '账面价值',
  `actual_value` decimal(15,2) DEFAULT 0.00 COMMENT '实盘价值',
  `value_difference` decimal(15,2) DEFAULT 0.00 COMMENT '价值差异',
  `inventory_result` varchar(30) DEFAULT 'NORMAL' COMMENT '盘点结果(NORMAL-正常,SURPLUS-盈余,SHORTAGE-亏损,DAMAGED-损坏,SCRAPPED-报废,LOST-丢失,LOCATION_CHANGED-位置变更,STATUS_CHANGED-状态变更,INFO_ERROR-信息错误,PENDING_INVESTIGATION-待查明)',
  `book_location` varchar(200) DEFAULT NULL COMMENT '账面位置',
  `actual_location` varchar(200) DEFAULT NULL COMMENT '实际位置',
  `book_status` varchar(20) DEFAULT NULL COMMENT '账面状态',
  `actual_status` varchar(20) DEFAULT NULL COMMENT '实际状态',
  `book_dept_id` varchar(32) DEFAULT NULL COMMENT '账面使用部门ID',
  `book_dept_name` varchar(100) DEFAULT NULL COMMENT '账面使用部门名称',
  `actual_dept_id` varchar(32) DEFAULT NULL COMMENT '实际使用部门ID',
  `actual_dept_name` varchar(100) DEFAULT NULL COMMENT '实际使用部门名称',
  `book_user_id` varchar(32) DEFAULT NULL COMMENT '账面使用人ID',
  `book_user_name` varchar(50) DEFAULT NULL COMMENT '账面使用人姓名',
  `actual_user_id` varchar(32) DEFAULT NULL COMMENT '实际使用人ID',
  `actual_user_name` varchar(50) DEFAULT NULL COMMENT '实际使用人姓名',
  `inventory_person_id` varchar(32) DEFAULT NULL COMMENT '盘点人员ID',
  `inventory_person_name` varchar(50) DEFAULT NULL COMMENT '盘点人员姓名',
  `inventory_time` datetime DEFAULT NULL COMMENT '盘点时间',
  `inventory_method` varchar(20) DEFAULT 'VISUAL' COMMENT '盘点方式(VISUAL-目视,SCAN-扫码,RFID-射频,GPS-定位)',
  `difference_reason` varchar(500) DEFAULT NULL COMMENT '差异原因',
  `difference_description` longtext COMMENT '差异说明',
  `handling_suggestion` longtext COMMENT '处理建议',
  `handling_result` longtext COMMENT '处理结果',
  `handler_id` varchar(32) DEFAULT NULL COMMENT '处理人ID',
  `handler_name` varchar(50) DEFAULT NULL COMMENT '处理人姓名',
  `handling_time` datetime DEFAULT NULL COMMENT '处理时间',
  `confirmer_id` varchar(32) DEFAULT NULL COMMENT '确认人ID',
  `confirmer_name` varchar(50) DEFAULT NULL COMMENT '确认人姓名',
  `confirmed_time` datetime DEFAULT NULL COMMENT '确认时间',
  `confirm_status` varchar(20) DEFAULT 'PENDING' COMMENT '确认状态(PENDING-待确认,CONFIRMED-已确认,REJECTED-已拒绝)',
  `confirm_comments` longtext COMMENT '确认意见',
  `photo_attachments` longtext COMMENT '照片附件',
  `document_attachments` longtext COMMENT '文档附件',
  `gps_coordinates` varchar(100) DEFAULT NULL COMMENT 'GPS坐标',
  `qr_code` varchar(100) DEFAULT NULL COMMENT '二维码/条码',
  `rfid_tag` varchar(100) DEFAULT NULL COMMENT 'RFID标签',
  `remarks` longtext COMMENT '备注',
  `need_adjustment` int DEFAULT 0 COMMENT '是否需要调整(0-否,1-是)',
  `adjustment_status` varchar(20) DEFAULT NULL COMMENT '调整状态(PENDING-待调整,PROCESSING-调整中,COMPLETED-已完成)',
  `ext1` varchar(255) DEFAULT NULL COMMENT '扩展字段1',
  `ext2` varchar(255) DEFAULT NULL COMMENT '扩展字段2',
  `ext3` varchar(255) DEFAULT NULL COMMENT '扩展字段3',
  `ext4` varchar(255) DEFAULT NULL COMMENT '扩展字段4',
  `ext5` varchar(255) DEFAULT NULL COMMENT '扩展字段5',
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人ID',
  `is_deleted` int DEFAULT 0 COMMENT '逻辑删除(0-未删除,1-已删除)',
  PRIMARY KEY (`record_id`),
  KEY `idx_plan_id` (`plan_id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_equipment_id` (`equipment_id`),
  KEY `idx_inventory_result` (`inventory_result`),
  KEY `idx_inventory_person_id` (`inventory_person_id`),
  KEY `idx_inventory_time` (`inventory_time`),
  KEY `idx_confirm_status` (`confirm_status`),
  KEY `idx_need_adjustment` (`need_adjustment`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='设备盘点记录表';

-- 插入测试数据

-- 盘点计划测试数据
INSERT INTO `eq_inventory_plan` (`plan_id`, `plan_name`, `plan_number`, `inventory_type`, `inventory_scope`, `inventory_purpose`, `inventory_description`, `plan_status`, `planned_start_time`, `planned_end_time`, `supervisor_id`, `supervisor_name`, `organize_dept_id`, `organize_dept_name`, `participate_depts`, `inventory_personnel`, `estimated_equipment_count`, `estimated_total_value`, `inventory_requirements`, `notice_items`, `priority`, `tenant_id`, `create_by`) VALUES
('ip_001', '2024年第四季度全面盘点', 'INV_2024Q4_001', 'FULL_INVENTORY', '全公司所有设备资产', '年度资产清查，确保账实相符', '2024年第四季度全面设备盘点，包括所有部门的固定资产和低值易耗品', 'PREPARING', '2024-12-10 08:00:00', '2024-12-20 18:00:00', 'user_001', '张三', 'dept_001', '资产管理部', '[{"deptId":"dept_001","deptName":"资产管理部"},{"deptId":"dept_002","deptName":"设备部"},{"deptId":"dept_003","deptName":"行政部"}]', '[{"userId":"user_001","userName":"张三","role":"负责人"},{"userId":"user_002","userName":"李四","role":"盘点员"},{"userId":"user_003","userName":"王五","role":"盘点员"}]', 500, 5000000.00, '1.按区域分组盘点\n2.使用扫码方式确认\n3.发现问题及时记录\n4.异常情况立即上报', '1.盘点期间设备暂停使用\n2.注意安全操作\n3.保持现场整洁\n4.严格按照流程执行', 4, 'tenant_001', 'user_001'),
('ip_002', '办公设备专项盘点', 'INV_2024_OFFICE_001', 'SPECIAL_INVENTORY', '全公司办公设备', '办公设备专项清查', '针对办公电脑、打印机、空调等办公设备进行专项盘点', 'PLANNED', '2024-12-15 09:00:00', '2024-12-18 17:00:00', 'user_002', '李四', 'dept_003', '行政部', '[{"deptId":"dept_003","deptName":"行政部"},{"deptId":"dept_004","deptName":"IT部"}]', '[{"userId":"user_002","userName":"李四","role":"负责人"},{"userId":"user_004","userName":"赵六","role":"盘点员"}]', 200, 1000000.00, '重点检查设备使用状态和位置变更情况', '注意电脑等设备的序列号核对', 3, 'tenant_001', 'user_002'),
('ip_003', '生产设备重点盘点', 'INV_2024_PROD_001', 'KEY_INVENTORY', '生产车间重要设备', '生产设备状态确认', '对生产车间的关键设备进行重点盘点，确保生产安全', 'PLANNED', '2024-12-12 08:00:00', '2024-12-14 18:00:00', 'user_003', '王五', 'dept_005', '生产部', '[{"deptId":"dept_005","deptName":"生产部"},{"deptId":"dept_002","deptName":"设备部"}]', '[{"userId":"user_003","userName":"王五","role":"负责人"},{"userId":"user_005","userName":"钱七","role":"盘点员"}]', 150, 8000000.00, '重点关注设备运行状态和安全性能', '盘点时注意生产安全，避免影响正常生产', 4, 'tenant_001', 'user_003');

-- 盘点任务测试数据
INSERT INTO `eq_inventory_task` (`task_id`, `task_number`, `task_name`, `plan_id`, `plan_name`, `inventory_area`, `inventory_dept_id`, `inventory_dept_name`, `inventory_location`, `task_status`, `planned_start_time`, `planned_end_time`, `inventory_person_id`, `inventory_person_name`, `assigned_by`, `assigned_by_name`, `assigned_time`, `estimated_equipment_count`, `estimated_total_value`, `task_description`, `inventory_requirements`, `priority`, `tenant_id`, `create_by`) VALUES
('it_001', 'TASK_INV_001', '1号楼设备盘点', 'ip_001', '2024年第四季度全面盘点', '1号楼1-5层', 'dept_003', '行政部', '1号楼各楼层办公区域', 'ASSIGNED', '2024-12-10 08:00:00', '2024-12-12 18:00:00', 'user_002', '李四', 'user_001', '张三', '2024-12-09 10:00:00', 80, 800000.00, '对1号楼所有办公设备进行全面盘点', '逐层逐间进行盘点，确保不遗漏', 3, 'tenant_001', 'user_001'),
('it_002', 'TASK_INV_002', '2号楼设备盘点', 'ip_001', '2024年第四季度全面盘点', '2号楼1-8层', 'dept_004', 'IT部', '2号楼各楼层办公区域', 'PLANNED', '2024-12-13 08:00:00', '2024-12-15 18:00:00', 'user_004', '赵六', 'user_001', '张三', '2024-12-09 10:00:00', 120, 1200000.00, '对2号楼所有设备进行全面盘点', '重点关注IT设备的序列号和配置信息', 3, 'tenant_001', 'user_001'),
('it_003', 'TASK_INV_003', '生产车间A区盘点', 'ip_003', '生产设备重点盘点', '生产车间A区', 'dept_005', '生产部', '生产车间A区各工位', 'PLANNED', '2024-12-12 08:00:00', '2024-12-13 18:00:00', 'user_005', '钱七', 'user_003', '王五', '2024-12-11 14:00:00', 50, 3000000.00, '对生产车间A区设备进行重点盘点', '注意设备运行状态和安全性能检查', 4, 'tenant_001', 'user_003');

-- 盘点记录测试数据
INSERT INTO `eq_inventory_record` (`record_id`, `record_number`, `plan_id`, `task_id`, `equipment_id`, `equipment_name`, `equipment_code`, `equipment_model`, `equipment_type_id`, `equipment_type_name`, `book_quantity`, `actual_quantity`, `difference_quantity`, `book_value`, `actual_value`, `value_difference`, `inventory_result`, `book_location`, `actual_location`, `book_status`, `actual_status`, `book_dept_id`, `book_dept_name`, `actual_dept_id`, `actual_dept_name`, `inventory_person_id`, `inventory_person_name`, `inventory_time`, `inventory_method`, `confirm_status`, `tenant_id`, `create_by`) VALUES
('ir_001', 'REC_INV_001', 'ip_001', 'it_001', 'eq_001', '1号楼中央空调', 'HVAC_001', 'VRV系列', 'et_001', '空调设备', 1, 1, 0, 50000.00, 50000.00, 0.00, 'NORMAL', '1号楼机房', '1号楼机房', '正常', '正常', 'dept_003', '行政部', 'dept_003', '行政部', 'user_002', '李四', '2024-12-10 09:00:00', 'SCAN', 'CONFIRMED', 'tenant_001', 'user_002'),
('ir_002', 'REC_INV_002', 'ip_001', 'it_001', 'eq_002', '办公楼客梯', 'ELEVATOR_001', 'OTIS系列', 'et_002', '电梯设备', 1, 1, 0, 100000.00, 100000.00, 0.00, 'NORMAL', '办公楼1-10层', '办公楼1-10层', '正常', '正常', 'dept_003', '行政部', 'dept_003', '行政部', 'user_002', '李四', '2024-12-10 10:30:00', 'VISUAL', 'CONFIRMED', 'tenant_001', 'user_002'),
('ir_003', 'REC_INV_003', 'ip_002', 'it_002', 'eq_004', '会议室投影仪', 'PROJ_001', 'EPSON系列', 'et_004', '投影设备', 1, 0, -1, 8000.00, 0.00, -8000.00, 'LOST', '1号楼301会议室', '未找到', '正常', '丢失', 'dept_003', '行政部', NULL, NULL, 'user_004', '赵六', '2024-12-11 14:00:00', 'VISUAL', 'PENDING_INVESTIGATION', 'tenant_001', 'user_004'),
('ir_004', 'REC_INV_004', 'ip_002', 'it_002', 'eq_005', '办公电脑', 'PC_001', 'DELL Optiplex', 'et_005', '计算机设备', 1, 1, 0, 5000.00, 5000.00, 0.00, 'LOCATION_CHANGED', '1号楼205办公室', '1号楼208办公室', '正常', '正常', 'dept_003', '行政部', 'dept_003', '行政部', 'user_004', '赵六', '2024-12-11 15:30:00', 'SCAN', 'PENDING', 'tenant_001', 'user_004'),
('ir_005', 'REC_INV_005', 'ip_003', 'it_003', 'eq_006', '数控机床', 'CNC_001', 'FANUC系列', 'et_006', '机床设备', 1, 1, 0, 500000.00, 480000.00, -20000.00, 'STATUS_CHANGED', '生产车间A区工位1', '生产车间A区工位1', '正常', '需要维修', 'dept_005', '生产部', 'dept_005', '生产部', 'user_005', '钱七', '2024-12-12 11:00:00', 'VISUAL', 'CONFIRMED', 'tenant_001', 'user_005');