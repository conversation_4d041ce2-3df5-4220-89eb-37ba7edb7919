<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.simple.equipment.mapper.InspectionTemplateItemMapper">

    <resultMap id="BaseResultMap" type="org.simple.equipment.entity.InspectionTemplateItem">
        <id column="id" property="id" />
        <result column="template_id" property="templateId" />
        <result column="item_code" property="itemCode" />
        <result column="item_name" property="itemName" />
        <result column="item_type" property="itemType" />
        <result column="item_category" property="itemCategory" />
        <result column="check_content" property="checkContent" />
        <result column="check_method" property="checkMethod" />
        <result column="check_standard" property="checkStandard" />
        <result column="data_type" property="dataType" />
        <result column="unit" property="unit" />
        <result column="normal_range" property="normalRange" />
        <result column="min_value" property="minValue" />
        <result column="max_value" property="maxValue" />
        <result column="default_value" property="defaultValue" />
        <result column="select_options" property="selectOptions" />
        <result column="is_required" property="isRequired" />
        <result column="is_key_item" property="isKeyItem" />
        <result column="exception_handling" property="exceptionHandling" />
        <result column="hint_message" property="hintMessage" />
        <result column="reference_image" property="referenceImage" />
        <result column="sort_order" property="sortOrder" />
        <result column="status" property="status" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_date" property="createDate" />
        <result column="update_date" property="updateDate" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="remark" property="remark" />
    </resultMap>

    <sql id="Base_Column_List">
        id, template_id, item_code, item_name, item_type, item_category, 
        check_content, check_method, check_standard, data_type, unit, 
        normal_range, min_value, max_value, default_value, select_options, 
        is_required, is_key_item, exception_handling, hint_message, 
        reference_image, sort_order, status, tenant_id, create_date, 
        update_date, create_by, update_by, remark
    </sql>

    <select id="selectItemPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM equipment_inspection_template_item
        WHERE tenant_id = #{tenantId}
        <if test="templateId != null and templateId != ''">
            AND template_id = #{templateId}
        </if>
        <if test="itemName != null and itemName != ''">
            AND item_name LIKE CONCAT('%', #{itemName}, '%')
        </if>
        <if test="itemType != null and itemType != ''">
            AND item_type = #{itemType}
        </if>
        <if test="itemCategory != null and itemCategory != ''">
            AND item_category = #{itemCategory}
        </if>
        ORDER BY sort_order ASC, create_date DESC
    </select>

    <select id="selectItemsByTemplate" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM equipment_inspection_template_item
        WHERE template_id = #{templateId}
        AND tenant_id = #{tenantId}
        AND status = '0'
        ORDER BY sort_order ASC
    </select>

    <select id="selectItemsByType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM equipment_inspection_template_item
        WHERE template_id = #{templateId}
        AND item_type = #{itemType}
        AND tenant_id = #{tenantId}
        AND status = '0'
        ORDER BY sort_order ASC
    </select>

    <select id="selectKeyItems" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM equipment_inspection_template_item
        WHERE template_id = #{templateId}
        AND tenant_id = #{tenantId}
        AND is_key_item = '1'
        AND status = '0'
        ORDER BY sort_order ASC
    </select>

    <select id="checkItemCodeExists" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM equipment_inspection_template_item
        WHERE item_code = #{itemCode}
        AND template_id = #{templateId}
        AND tenant_id = #{tenantId}
        <if test="excludeId != null and excludeId != ''">
            AND id != #{excludeId}
        </if>
    </select>

    <insert id="batchInsertItems">
        INSERT INTO equipment_inspection_template_item (
            id, template_id, item_code, item_name, item_type, item_category,
            check_content, check_method, check_standard, data_type, unit,
            normal_range, min_value, max_value, default_value, select_options,
            is_required, is_key_item, exception_handling, hint_message,
            reference_image, sort_order, status, tenant_id, create_date,
            create_by
        ) VALUES
        <foreach collection="items" item="item" separator=",">
            (
                #{item.id}, #{item.templateId}, #{item.itemCode}, #{item.itemName},
                #{item.itemType}, #{item.itemCategory}, #{item.checkContent},
                #{item.checkMethod}, #{item.checkStandard}, #{item.dataType},
                #{item.unit}, #{item.normalRange}, #{item.minValue}, #{item.maxValue},
                #{item.defaultValue}, #{item.selectOptions}, #{item.isRequired},
                #{item.isKeyItem}, #{item.exceptionHandling}, #{item.hintMessage},
                #{item.referenceImage}, #{item.sortOrder}, #{item.status},
                #{item.tenantId}, #{item.createDate}, #{item.createBy}
            )
        </foreach>
    </insert>

    <delete id="batchDeleteItems">
        DELETE FROM equipment_inspection_template_item
        WHERE template_id = #{templateId}
        AND tenant_id = #{tenantId}
    </delete>

    <update id="updateItemSortOrder">
        UPDATE equipment_inspection_template_item
        SET sort_order = #{sortOrder},
            update_date = NOW()
        WHERE id = #{itemId}
    </update>

    <insert id="copyTemplateItems">
        INSERT INTO equipment_inspection_template_item (
            id, template_id, item_code, item_name, item_type, item_category,
            check_content, check_method, check_standard, data_type, unit,
            normal_range, min_value, max_value, default_value, select_options,
            is_required, is_key_item, exception_handling, hint_message,
            reference_image, sort_order, status, tenant_id, create_date,
            create_by
        )
        SELECT
            UUID(), #{targetTemplateId}, item_code, item_name, item_type, item_category,
            check_content, check_method, check_standard, data_type, unit,
            normal_range, min_value, max_value, default_value, select_options,
            is_required, is_key_item, exception_handling, hint_message,
            reference_image, sort_order, status, #{tenantId}, NOW(), create_by
        FROM equipment_inspection_template_item
        WHERE template_id = #{sourceTemplateId}
        AND tenant_id = #{tenantId}
        AND status = '0'
    </insert>

</mapper>