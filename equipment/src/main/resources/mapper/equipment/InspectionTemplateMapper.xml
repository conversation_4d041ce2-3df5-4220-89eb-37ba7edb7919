<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.simple.equipment.mapper.InspectionTemplateMapper">

    <resultMap id="BaseResultMap" type="org.simple.equipment.entity.InspectionTemplate">
        <id column="id" property="id" />
        <result column="template_code" property="templateCode" />
        <result column="template_name" property="templateName" />
        <result column="category_id" property="categoryId" />
        <result column="category_name" property="categoryName" />
        <result column="equipment_model" property="equipmentModel" />
        <result column="cycle_type" property="cycleType" />
        <result column="cycle_value" property="cycleValue" />
        <result column="template_type" property="templateType" />
        <result column="template_status" property="templateStatus" />
        <result column="estimated_duration" property="estimatedDuration" />
        <result column="priority_level" property="priorityLevel" />
        <result column="template_description" property="templateDescription" />
        <result column="inspection_requirements" property="inspectionRequirements" />
        <result column="safety_notes" property="safetyNotes" />
        <result column="required_tools" property="requiredTools" />
        <result column="applicable_departments" property="applicableDepartments" />
        <result column="creator_name" property="creatorName" />
        <result column="reviewer_id" property="reviewerId" />
        <result column="reviewer_name" property="reviewerName" />
        <result column="review_time" property="reviewTime" />
        <result column="review_comment" property="reviewComment" />
        <result column="publish_time" property="publishTime" />
        <result column="effective_time" property="effectiveTime" />
        <result column="expiry_time" property="expiryTime" />
        <result column="version_number" property="versionNumber" />
        <result column="status" property="status" />
        <result column="sort_order" property="sortOrder" />
        <result column="usage_count" property="usageCount" />
        <result column="last_used_time" property="lastUsedTime" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_date" property="createDate" />
        <result column="update_date" property="updateDate" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="remark" property="remark" />
    </resultMap>

    <sql id="Base_Column_List">
        id, template_code, template_name, category_id, category_name, equipment_model, 
        cycle_type, cycle_value, template_type, template_status, estimated_duration, 
        priority_level, template_description, inspection_requirements, safety_notes, 
        required_tools, applicable_departments, creator_name, reviewer_id, reviewer_name, 
        review_time, review_comment, publish_time, effective_time, expiry_time, 
        version_number, status, sort_order, usage_count, last_used_time, tenant_id, 
        create_date, update_date, create_by, update_by, remark
    </sql>

    <select id="selectTemplatePage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM equipment_inspection_template
        WHERE tenant_id = #{tenantId}
        <if test="templateCode != null and templateCode != ''">
            AND template_code LIKE CONCAT('%', #{templateCode}, '%')
        </if>
        <if test="templateName != null and templateName != ''">
            AND template_name LIKE CONCAT('%', #{templateName}, '%')
        </if>
        <if test="categoryId != null and categoryId != ''">
            AND category_id = #{categoryId}
        </if>
        <if test="cycleType != null and cycleType != ''">
            AND cycle_type = #{cycleType}
        </if>
        <if test="templateStatus != null and templateStatus != ''">
            AND template_status = #{templateStatus}
        </if>
        <if test="templateType != null and templateType != ''">
            AND template_type = #{templateType}
        </if>
        ORDER BY sort_order ASC, create_date DESC
    </select>

    <select id="selectTemplatesByCategory" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM equipment_inspection_template
        WHERE tenant_id = #{tenantId}
        AND category_id = #{categoryId}
        AND status = '0'
        AND template_status = 'PUBLISHED'
        ORDER BY sort_order ASC
    </select>

    <select id="selectTemplatesByModel" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM equipment_inspection_template
        WHERE tenant_id = #{tenantId}
        AND (equipment_model = #{equipmentModel} OR equipment_model IS NULL OR equipment_model = '')
        AND status = '0'
        AND template_status = 'PUBLISHED'
        ORDER BY sort_order ASC
    </select>

    <select id="selectPublishedTemplates" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM equipment_inspection_template
        WHERE tenant_id = #{tenantId}
        AND template_status = 'PUBLISHED'
        AND status = '0'
        AND (effective_time IS NULL OR effective_time &lt;= NOW())
        AND (expiry_time IS NULL OR expiry_time &gt; NOW())
        ORDER BY sort_order ASC
    </select>

    <select id="selectTemplatesByCycle" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM equipment_inspection_template
        WHERE tenant_id = #{tenantId}
        AND cycle_type = #{cycleType}
        AND status = '0'
        AND template_status = 'PUBLISHED'
        ORDER BY sort_order ASC
    </select>

    <select id="checkTemplateCodeExists" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM equipment_inspection_template
        WHERE template_code = #{templateCode}
        AND tenant_id = #{tenantId}
        <if test="excludeId != null and excludeId != ''">
            AND id != #{excludeId}
        </if>
    </select>

    <update id="updateUsageCount">
        UPDATE equipment_inspection_template
        SET usage_count = COALESCE(usage_count, 0) + 1,
            last_used_time = NOW()
        WHERE id = #{templateId}
    </update>

    <update id="batchUpdateStatus">
        UPDATE equipment_inspection_template
        SET status = #{status},
            update_date = NOW()
        WHERE id IN
        <foreach collection="templateIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND tenant_id = #{tenantId}
    </update>

    <select id="selectTemplateStatistics" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM equipment_inspection_template
        WHERE tenant_id = #{tenantId}
        ORDER BY usage_count DESC, create_date DESC
        LIMIT 10
    </select>

</mapper>