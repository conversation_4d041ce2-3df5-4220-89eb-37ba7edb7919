<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.simple.equipment.mapper.InspectionApprovalFlowMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="org.simple.equipment.entity.InspectionApprovalFlow">
        <id column="flow_id" property="flowId" />
        <result column="approval_id" property="approvalId" />
        <result column="inspection_task_id" property="inspectionTaskId" />
        <result column="task_code" property="taskCode" />
        <result column="approval_node" property="approvalNode" />
        <result column="node_name" property="nodeName" />
        <result column="node_type" property="nodeType" />
        <result column="approver_id" property="approverId" />
        <result column="approver_name" property="approverName" />
        <result column="approver_role" property="approverRole" />
        <result column="approval_status" property="approvalStatus" />
        <result column="approval_result" property="approvalResult" />
        <result column="approval_comment" property="approvalComment" />
        <result column="approval_time" property="approvalTime" />
        <result column="arrive_time" property="arriveTime" />
        <result column="process_duration" property="processDuration" />
        <result column="is_auto_approval" property="isAutoApproval" />
        <result column="auto_approval_rule" property="autoApprovalRule" />
        <result column="next_node" property="nextNode" />
        <result column="next_approver" property="nextApprover" />
        <result column="delegate_from" property="delegateFrom" />
        <result column="delegate_from_name" property="delegateFromName" />
        <result column="approval_attachments" property="approvalAttachments" />
        <result column="extra_data" property="extraData" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
    </resultMap>

    <!-- 根据审核ID查询流程记录 -->
    <select id="selectByApprovalId" resultMap="BaseResultMap">
        SELECT f.*
        FROM eq_inspection_approval_flow f
        WHERE f.approval_id = #{approvalId}
          AND f.tenant_id = #{tenantId}
        ORDER BY f.approval_node ASC
    </select>

    <!-- 查询当前审核节点 -->
    <select id="selectCurrentNode" resultMap="BaseResultMap">
        SELECT f.*
        FROM eq_inspection_approval_flow f
        WHERE f.approval_id = #{approvalId}
          AND f.tenant_id = #{tenantId}
          AND f.approval_status = 'PENDING'
        ORDER BY f.approval_node ASC
        LIMIT 1
    </select>

    <!-- 查询下一个审核节点 -->
    <select id="selectNextNode" resultMap="BaseResultMap">
        SELECT f.*
        FROM eq_inspection_approval_flow f
        WHERE f.approval_id = #{approvalId}
          AND f.tenant_id = #{tenantId}
          AND f.approval_node > #{currentNode}
        ORDER BY f.approval_node ASC
        LIMIT 1
    </select>

    <!-- 查询待处理的流程节点 -->
    <select id="selectPendingNodes" resultMap="BaseResultMap">
        SELECT f.*, a.task_code, a.asset_name, a.submit_time
        FROM eq_inspection_approval_flow f
        INNER JOIN eq_inspection_approval a ON f.approval_id = a.approval_id
        WHERE f.approver_id = #{approverId}
          AND f.tenant_id = #{tenantId}
          AND f.approval_status = 'PENDING'
        ORDER BY a.submit_time ASC
    </select>

    <!-- 统计审核流程效率 -->
    <select id="selectFlowEfficiency" resultType="map">
        SELECT 
            node_name,
            COUNT(*) as total_count,
            AVG(process_duration) as avg_duration,
            MIN(process_duration) as min_duration,
            MAX(process_duration) as max_duration,
            SUM(CASE WHEN approval_result = 'PASS' THEN 1 ELSE 0 END) as pass_count,
            SUM(CASE WHEN approval_result = 'REJECT' THEN 1 ELSE 0 END) as reject_count
        FROM eq_inspection_approval_flow
        <where>
            tenant_id = #{params.tenantId}
            AND approval_status IN ('APPROVED', 'REJECTED')
            AND process_duration IS NOT NULL
            <if test="params.startTime != null">
                AND approval_time >= #{params.startTime}
            </if>
            <if test="params.endTime != null">
                AND approval_time &lt;= #{params.endTime}
            </if>
            <if test="params.nodeType != null and params.nodeType != ''">
                AND node_type = #{params.nodeType}
            </if>
        </where>
        GROUP BY node_name
        ORDER BY avg_duration DESC
    </select>

    <!-- 查询审核路径 -->
    <select id="selectApprovalPath" resultMap="BaseResultMap">
        SELECT f.*
        FROM eq_inspection_approval_flow f
        WHERE f.inspection_task_id = #{inspectionTaskId}
          AND f.tenant_id = #{tenantId}
        ORDER BY f.approval_node ASC, f.create_time ASC
    </select>

    <!-- 统计节点处理时长 -->
    <select id="selectNodeDurationStats" resultType="map">
        SELECT 
            COUNT(*) as total_count,
            AVG(process_duration) as avg_duration,
            MIN(process_duration) as min_duration,
            MAX(process_duration) as max_duration,
            STDDEV(process_duration) as std_duration
        FROM eq_inspection_approval_flow
        WHERE tenant_id = #{tenantId}
          AND approval_status IN ('APPROVED', 'REJECTED')
          AND process_duration IS NOT NULL
        <if test="nodeType != null and nodeType != ''">
            AND node_type = #{nodeType}
        </if>
        <if test="approverId != null and approverId != ''">
            AND approver_id = #{approverId}
        </if>
    </select>

    <!-- 查询代理审核记录 -->
    <select id="selectDelegateRecords" resultMap="BaseResultMap">
        SELECT f.*, a.task_code, a.asset_name
        FROM eq_inspection_approval_flow f
        INNER JOIN eq_inspection_approval a ON f.approval_id = a.approval_id
        WHERE f.delegate_from = #{delegateFrom}
          AND f.tenant_id = #{tenantId}
        ORDER BY f.approval_time DESC
    </select>

</mapper>