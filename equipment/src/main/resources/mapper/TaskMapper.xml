<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.simple.equipment.mapper.TaskMapper">

    <!-- 基本字段映射 -->
    <resultMap id="BaseResultMap" type="org.simple.equipment.entity.Task">
        <id column="id" property="id"/>
        <result column="task_code" property="taskCode"/>
        <result column="task_name" property="taskName"/>
        <result column="task_type_id" property="taskTypeId"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="assigned_user_id" property="assignedUserId"/>
        <result column="assigned_user_name" property="assignedUserName"/>
        <result column="priority" property="priority"/>
        <result column="planned_start_time" property="plannedStartTime"/>
        <result column="planned_end_time" property="plannedEndTime"/>
        <result column="actual_start_time" property="actualStartTime"/>
        <result column="actual_end_time" property="actualEndTime"/>
        <result column="task_status" property="taskStatus"/>
        <result column="content" property="content"/>
        <result column="result" property="result"/>
        <result column="remark" property="remark"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_date" property="createDate"/>
        <result column="update_date" property="updateDate"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
    </resultMap>

    <!-- 获取任务统计信息 -->
    <select id="getTaskStatistics" parameterType="string" resultType="map">
        SELECT 
            task_status,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM equipment_task WHERE tenant_id = #{tenantId}), 2) as percentage
        FROM equipment_task 
        WHERE tenant_id = #{tenantId}
        GROUP BY task_status
        ORDER BY task_status
    </select>

    <!-- 获取逾期任务列表 -->
    <select id="getOverdueTasks" parameterType="string" resultMap="BaseResultMap">
        SELECT * FROM equipment_task
        WHERE tenant_id = #{tenantId}
        AND planned_end_time &lt; NOW()
        AND task_status IN ('PENDING', 'PROCESSING')
        ORDER BY planned_end_time ASC
    </select>

    <!-- 获取用户的任务列表 -->
    <select id="getUserTasks" resultMap="BaseResultMap">
        SELECT * FROM equipment_task
        WHERE assigned_user_id = #{userId}
        AND tenant_id = #{tenantId}
        AND task_status IN ('PENDING', 'PROCESSING')
        ORDER BY priority DESC, planned_start_time ASC
    </select>

    <!-- 获取设备的任务列表 -->
    <select id="getEquipmentTasks" resultMap="BaseResultMap">
        SELECT * FROM equipment_task
        WHERE equipment_id = #{equipmentId}
        AND tenant_id = #{tenantId}
        ORDER BY create_date DESC
    </select>

</mapper>