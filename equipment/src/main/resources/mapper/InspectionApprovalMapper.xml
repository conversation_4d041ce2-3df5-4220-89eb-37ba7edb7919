<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.simple.equipment.mapper.InspectionApprovalMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="org.simple.equipment.entity.InspectionApproval">
        <id column="approval_id" property="approvalId" />
        <result column="inspection_task_id" property="inspectionTaskId" />
        <result column="task_code" property="taskCode" />
        <result column="asset_id" property="assetId" />
        <result column="asset_name" property="assetName" />
        <result column="template_id" property="templateId" />
        <result column="template_name" property="templateName" />
        <result column="executor_id" property="executorId" />
        <result column="executor_name" property="executorName" />
        <result column="executor_dept" property="executorDept" />
        <result column="approval_status" property="approvalStatus" />
        <result column="approval_level" property="approvalLevel" />
        <result column="current_node" property="currentNode" />
        <result column="approver_id" property="approverId" />
        <result column="approver_name" property="approverName" />
        <result column="approval_time" property="approvalTime" />
        <result column="approval_comment" property="approvalComment" />
        <result column="approval_result" property="approvalResult" />
        <result column="inspection_time" property="inspectionTime" />
        <result column="submit_time" property="submitTime" />
        <result column="total_items" property="totalItems" />
        <result column="normal_items" property="normalItems" />
        <result column="abnormal_items" property="abnormalItems" />
        <result column="critical_abnormal" property="criticalAbnormal" />
        <result column="photo_count" property="photoCount" />
        <result column="inspection_score" property="inspectionScore" />
        <result column="quality_level" property="qualityLevel" />
        <result column="need_follow_up" property="needFollowUp" />
        <result column="follow_up_requirement" property="followUpRequirement" />
        <result column="follow_up_deadline" property="followUpDeadline" />
        <result column="attachment_files" property="attachmentFiles" />
        <result column="remark" property="remark" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
    </resultMap>

    <!-- 分页查询审核记录 -->
    <select id="selectApprovalPage" resultMap="BaseResultMap">
        SELECT a.*, 
               asset.asset_code,
               asset.location_name,
               dept.dept_name as executor_dept_name
        FROM eq_inspection_approval a
        LEFT JOIN eq_asset asset ON a.asset_id = asset.asset_id
        LEFT JOIN sys_dept dept ON a.executor_dept = dept.dept_id
        <where>
            a.tenant_id = #{params.tenantId}
            <if test="params.approvalStatus != null and params.approvalStatus != ''">
                AND a.approval_status = #{params.approvalStatus}
            </if>
            <if test="params.qualityLevel != null and params.qualityLevel != ''">
                AND a.quality_level = #{params.qualityLevel}
            </if>
            <if test="params.assetName != null and params.assetName != ''">
                AND a.asset_name LIKE CONCAT('%', #{params.assetName}, '%')
            </if>
            <if test="params.executorName != null and params.executorName != ''">
                AND a.executor_name LIKE CONCAT('%', #{params.executorName}, '%')
            </if>
            <if test="params.approverName != null and params.approverName != ''">
                AND a.approver_name LIKE CONCAT('%', #{params.approverName}, '%')
            </if>
            <if test="params.startTime != null">
                AND a.submit_time >= #{params.startTime}
            </if>
            <if test="params.endTime != null">
                AND a.submit_time &lt;= #{params.endTime}
            </if>
            <if test="params.needFollowUp != null">
                AND a.need_follow_up = #{params.needFollowUp}
            </if>
        </where>
        ORDER BY a.submit_time DESC
    </select>

    <!-- 查询待审核记录 -->
    <select id="selectPendingApprovals" resultMap="BaseResultMap">
        SELECT a.*
        FROM eq_inspection_approval a
        INNER JOIN eq_inspection_approval_flow f ON a.approval_id = f.approval_id
        WHERE a.tenant_id = #{tenantId}
          AND a.approval_status = 'PENDING'
          AND f.approver_id = #{approverId}
          AND f.approval_status = 'PENDING'
        ORDER BY a.submit_time ASC
    </select>

    <!-- 查询已审核记录 -->
    <select id="selectApprovedRecords" resultMap="BaseResultMap">
        SELECT a.*
        FROM eq_inspection_approval a
        WHERE a.tenant_id = #{tenantId}
          AND a.approver_id = #{approverId}
          AND a.approval_status IN ('APPROVED', 'REJECTED')
        ORDER BY a.approval_time DESC
    </select>

    <!-- 统计审核数据 -->
    <select id="selectApprovalStatistics" resultType="map">
        SELECT 
            COUNT(*) as total_count,
            SUM(CASE WHEN approval_status = 'PENDING' THEN 1 ELSE 0 END) as pending_count,
            SUM(CASE WHEN approval_status = 'APPROVED' THEN 1 ELSE 0 END) as approved_count,
            SUM(CASE WHEN approval_status = 'REJECTED' THEN 1 ELSE 0 END) as rejected_count,
            SUM(CASE WHEN quality_level = 'EXCELLENT' THEN 1 ELSE 0 END) as excellent_count,
            SUM(CASE WHEN quality_level = 'GOOD' THEN 1 ELSE 0 END) as good_count,
            SUM(CASE WHEN quality_level = 'QUALIFIED' THEN 1 ELSE 0 END) as qualified_count,
            SUM(CASE WHEN quality_level = 'UNQUALIFIED' THEN 1 ELSE 0 END) as unqualified_count,
            AVG(inspection_score) as avg_score,
            SUM(abnormal_items) as total_abnormal,
            SUM(critical_abnormal) as total_critical
        FROM eq_inspection_approval
        <where>
            tenant_id = #{params.tenantId}
            <if test="params.startTime != null">
                AND submit_time >= #{params.startTime}
            </if>
            <if test="params.endTime != null">
                AND submit_time &lt;= #{params.endTime}
            </if>
            <if test="params.deptId != null and params.deptId != ''">
                AND executor_dept = #{params.deptId}
            </if>
        </where>
    </select>

    <!-- 查询超时审核记录 -->
    <select id="selectTimeoutApprovals" resultMap="BaseResultMap">
        SELECT a.*
        FROM eq_inspection_approval a
        WHERE a.tenant_id = #{tenantId}
          AND a.approval_status = 'PENDING'
          AND TIMESTAMPDIFF(HOUR, a.submit_time, NOW()) > #{timeoutHours}
    </select>

    <!-- 查询异常审核记录 -->
    <select id="selectAbnormalApprovals" resultMap="BaseResultMap">
        SELECT a.*
        FROM eq_inspection_approval a
        WHERE a.tenant_id = #{tenantId}
          AND (a.critical_abnormal > 0 OR a.quality_level = 'UNQUALIFIED')
          AND a.approval_status = 'PENDING'
        ORDER BY a.critical_abnormal DESC, a.submit_time ASC
    </select>

    <!-- 根据任务ID查询审核记录 -->
    <select id="selectByTaskId" resultMap="BaseResultMap">
        SELECT a.*
        FROM eq_inspection_approval a
        WHERE a.inspection_task_id = #{inspectionTaskId}
          AND a.tenant_id = #{tenantId}
    </select>

    <!-- 查询审核历史 -->
    <select id="selectApprovalHistory" resultMap="BaseResultMap">
        SELECT a.*
        FROM eq_inspection_approval a
        WHERE a.tenant_id = #{tenantId}
          AND a.asset_id = #{assetId}
          AND a.approval_status IN ('APPROVED', 'REJECTED')
        <if test="startTime != null">
            AND a.inspection_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND a.inspection_time &lt;= #{endTime}
        </if>
        ORDER BY a.inspection_time DESC
    </select>

    <!-- 统计质量等级分布 -->
    <select id="selectQualityDistribution" resultType="map">
        SELECT 
            quality_level,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
        FROM eq_inspection_approval
        <where>
            tenant_id = #{params.tenantId}
            AND approval_status = 'APPROVED'
            <if test="params.startTime != null">
                AND approval_time >= #{params.startTime}
            </if>
            <if test="params.endTime != null">
                AND approval_time &lt;= #{params.endTime}
            </if>
        </where>
        GROUP BY quality_level
        ORDER BY 
            CASE quality_level
                WHEN 'EXCELLENT' THEN 1
                WHEN 'GOOD' THEN 2
                WHEN 'QUALIFIED' THEN 3
                WHEN 'UNQUALIFIED' THEN 4
            END
    </select>

    <!-- 查询审核工作量统计 -->
    <select id="selectApprovalWorkload" resultType="map">
        SELECT 
            approver_id,
            approver_name,
            COUNT(*) as approval_count,
            AVG(TIMESTAMPDIFF(MINUTE, submit_time, approval_time)) as avg_duration,
            SUM(CASE WHEN approval_result = 'PASS' THEN 1 ELSE 0 END) as pass_count,
            SUM(CASE WHEN approval_result = 'REJECT' THEN 1 ELSE 0 END) as reject_count
        FROM eq_inspection_approval
        WHERE tenant_id = #{tenantId}
          AND approval_status IN ('APPROVED', 'REJECTED')
          AND approval_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY approver_id, approver_name
        ORDER BY approval_count DESC
    </select>

</mapper>