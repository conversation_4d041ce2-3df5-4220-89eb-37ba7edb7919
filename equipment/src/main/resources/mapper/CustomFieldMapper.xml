<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.simple.equipment.mapper.CustomFieldMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="org.simple.equipment.entity.CustomField">
        <id column="id" property="id"/>
        <result column="field_code" property="fieldCode"/>
        <result column="field_name" property="fieldName"/>
        <result column="field_type" property="fieldType"/>
        <result column="category_id" property="categoryId"/>
        <result column="type_id" property="typeId"/>
        <result column="default_value" property="defaultValue"/>
        <result column="options" property="options"/>
        <result column="validation_rules" property="validationRules"/>
        <result column="is_required" property="isRequired"/>
        <result column="is_enabled" property="isEnabled"/>
        <result column="display_order" property="displayOrder"/>
        <result column="placeholder" property="placeholder"/>
        <result column="help_text" property="helpText"/>
        <result column="field_group" property="fieldGroup"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_date" property="createDate"/>
        <result column="update_date" property="updateDate"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, field_code, field_name, field_type, category_id, type_id, default_value, options,
        validation_rules, is_required, is_enabled, display_order, placeholder, help_text,
        field_group, tenant_id, create_date, update_date, creator, updater
    </sql>

    <!-- 查询有效的自定义字段列表 -->
    <select id="selectActiveList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM equipment_custom_field
        WHERE tenant_id = #{tenantId}
        AND is_enabled = '1'
        ORDER BY display_order ASC, create_date DESC
    </select>

    <!-- 根据设备分类和类型查询自定义字段 -->
    <select id="selectByCategory" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM equipment_custom_field
        WHERE tenant_id = #{tenantId}
        AND is_enabled = '1'
        AND (category_id IS NULL OR category_id = '' OR category_id = #{categoryId})
        <if test="typeId != null and typeId != ''">
            AND (type_id IS NULL OR type_id = '' OR type_id = #{typeId})
        </if>
        ORDER BY display_order ASC, create_date DESC
    </select>

    <!-- 根据字段编码查询字段 -->
    <select id="selectByFieldCode" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM equipment_custom_field
        WHERE tenant_id = #{tenantId}
        AND field_code = #{fieldCode}
        <if test="excludeId != null and excludeId != ''">
            AND id != #{excludeId}
        </if>
        LIMIT 1
    </select>

    <!-- 查询自定义字段列表 -->
    <select id="selectCustomFieldList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM equipment_custom_field
        WHERE tenant_id = #{tenantId}
        <if test="fieldCode != null and fieldCode != ''">
            AND field_code LIKE CONCAT('%', #{fieldCode}, '%')
        </if>
        <if test="fieldName != null and fieldName != ''">
            AND field_name LIKE CONCAT('%', #{fieldName}, '%')
        </if>
        <if test="fieldType != null and fieldType != ''">
            AND field_type = #{fieldType}
        </if>
        <if test="categoryId != null and categoryId != ''">
            AND category_id = #{categoryId}
        </if>
        <if test="typeId != null and typeId != ''">
            AND type_id = #{typeId}
        </if>
        <if test="isEnabled != null and isEnabled != ''">
            AND is_enabled = #{isEnabled}
        </if>
        <if test="fieldGroup != null and fieldGroup != ''">
            AND field_group = #{fieldGroup}
        </if>
        ORDER BY display_order ASC, create_date DESC
    </select>

    <!-- 根据分组查询字段 -->
    <select id="selectByGroup" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM equipment_custom_field
        WHERE tenant_id = #{tenantId}
        AND field_group = #{fieldGroup}
        AND is_enabled = '1'
        ORDER BY display_order ASC, create_date DESC
    </select>

    <!-- 查询最大显示顺序 -->
    <select id="selectMaxDisplayOrder" resultType="java.lang.Integer">
        SELECT MAX(display_order)
        FROM equipment_custom_field
        WHERE tenant_id = #{tenantId}
    </select>

</mapper>