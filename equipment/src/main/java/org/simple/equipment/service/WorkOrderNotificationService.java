package org.simple.equipment.service;

import org.simple.equipment.entity.WorkOrder;

/**
 * 工单通知服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public interface WorkOrderNotificationService {

    /**
     * 发送工单创建通知
     *
     * @param workOrder 工单信息
     */
    void sendCreateNotification(WorkOrder workOrder);

    /**
     * 发送工单分配通知
     *
     * @param workOrder 工单信息
     */
    void sendAssignNotification(WorkOrder workOrder);

    /**
     * 发送工单开始处理通知
     *
     * @param workOrder 工单信息
     */
    void sendStartNotification(WorkOrder workOrder);

    /**
     * 发送工单完成通知
     *
     * @param workOrder 工单信息
     */
    void sendCompleteNotification(WorkOrder workOrder);

    /**
     * 发送工单取消通知
     *
     * @param workOrder 工单信息
     */
    void sendCancelNotification(WorkOrder workOrder);

    /**
     * 发送工单催办通知
     *
     * @param workOrder 工单信息
     */
    void sendUrgeNotification(WorkOrder workOrder);

    /**
     * 发送工单超时提醒
     *
     * @param workOrder 工单信息
     */
    void sendTimeoutReminder(WorkOrder workOrder);
}