package org.simple.equipment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.simple.equipment.entity.OfflineTaskCache;

/**
 * 离线任务缓存服务接口
 * 
 * <AUTHOR>
 */
public interface OfflineTaskCacheService extends IService<OfflineTaskCache> {

    /**
     * 清理过期缓存
     * 
     * @param currentTime 当前时间戳
     * @return 清理数量
     */
    Integer cleanExpiredCache(Long currentTime);

    /**
     * 获取用户的缓存任务
     * 
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @return 缓存任务列表
     */
    java.util.List<OfflineTaskCache> getUserCachedTasks(String userId, String deviceId);

    /**
     * 缓存任务数据
     * 
     * @param taskCache 任务缓存
     * @return 缓存结果
     */
    Boolean cacheTaskData(OfflineTaskCache taskCache);

    /**
     * 更新访问记录
     * 
     * @param cacheId 缓存ID
     */
    void updateAccessRecord(String cacheId);
}