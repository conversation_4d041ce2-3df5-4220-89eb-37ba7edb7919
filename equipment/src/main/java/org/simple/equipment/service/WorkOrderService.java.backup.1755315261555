package org.simple.equipment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.simple.equipment.entity.WorkOrder;

import java.util.List;
import java.util.Map;

/**
 * 工单服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public interface WorkOrderService extends IService<WorkOrder> {

    /**
     * 创建工单
     *
     * @param workOrder 工单信息
     * @return 创建结果
     */
    boolean createWorkOrder(WorkOrder workOrder);

    /**
     * 更新工单
     *
     * @param workOrder 工单信息
     * @return 更新结果
     */
    boolean updateWorkOrder(WorkOrder workOrder);

    /**
     * 删除工单
     *
     * @param id 工单ID
     * @return 删除结果
     */
    boolean deleteWorkOrder(String id);

    /**
     * 分配工单
     *
     * @param orderId 工单ID
     * @param userId 用户ID
     * @param userName 用户名
     * @return 分配结果
     */
    boolean assignWorkOrder(String orderId, String userId, String userName);

    /**
     * 开始处理工单
     *
     * @param orderId 工单ID
     * @return 处理结果
     */
    boolean startWorkOrder(String orderId);

    /**
     * 完成工单
     *
     * @param orderId 工单ID
     * @param solution 解决方案
     * @return 完成结果
     */
    boolean completeWorkOrder(String orderId, String solution);

    /**
     * 取消工单
     *
     * @param orderId 工单ID
     * @param reason 取消原因
     * @return 取消结果
     */
    boolean cancelWorkOrder(String orderId, String reason);

    /**
     * 获取工单统计信息
     *
     * @return 统计结果
     */
    Map<String, Object> getWorkOrderStatistics();

    /**
     * 获取用户的工单列表
     *
     * @param userId 用户ID
     * @return 用户工单列表
     */
    List<WorkOrder> getUserWorkOrders(String userId);

    /**
     * 获取设备的工单列表
     *
     * @param equipmentId 设备ID
     * @return 设备工单列表
     */
    List<WorkOrder> getEquipmentWorkOrders(String equipmentId);

    /**
     * 获取待处理工单列表
     *
     * @return 待处理工单列表
     */
    List<WorkOrder> getPendingWorkOrders();

    /**
     * 获取超时工单列表
     *
     * @param hours 超时小时数
     * @return 超时工单列表
     */
    List<WorkOrder> getTimeoutWorkOrders(Integer hours);

    /**
     * 检查工单编码是否存在
     *
     * @param orderCode 工单编码
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean checkOrderCodeExists(String orderCode, String excludeId);

    /**
     * 生成工单编码
     *
     * @param orderType 工单类型
     * @return 工单编码
     */
    String generateOrderCode(String orderType);

    /**
     * 发送工单通知
     *
     * @param orderId 工单ID
     * @param type 通知类型
     */
    void sendWorkOrderNotification(String orderId, String type);

    /**
     * 自动分配工单
     *
     * @param orderId 工单ID
     * @return 分配结果
     */
    boolean autoAssignWorkOrder(String orderId);

    /**
     * 获取工单处理时长统计
     *
     * @return 处理时长统计
     */
    List<Map<String, Object>> getProcessingTimeStatistics();

    /**
     * 工单催办
     *
     * @param orderId 工单ID
     * @return 催办结果
     */
    boolean urgeWorkOrder(String orderId);

    /**
     * 为异常记录生成工单
     *
     * @param exceptionRecordId 异常记录ID
     * @return 工单ID
     */
    String generateWorkOrderForException(String exceptionRecordId);
}