package org.simple.equipment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.simple.base.vo.FrResult;
import org.simple.equipment.entity.TagCombination;

import java.util.List;
import java.util.Map;

/**
 * 标签组合服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public interface TagCombinationService extends IService<TagCombination> {

    /**
     * 查询有效的标签组合列表
     *
     * @return 组合列表
     */
    List<TagCombination> getActiveCombinationList();

    /**
     * 根据设备分类查询标签组合
     *
     * @param categoryId 分类ID
     * @param typeId 类型ID
     * @return 组合列表
     */
    List<TagCombination> getCombinationsByCategory(String categoryId, String typeId);

    /**
     * 查询标签组合列表
     *
     * @param tagCombination 查询条件
     * @return 组合列表
     */
    List<TagCombination> getCombinationList(TagCombination tagCombination);

    /**
     * 查询组合详情（包含标签信息）
     *
     * @param combinationId 组合ID
     * @return 组合详情
     */
    Map<String, Object> getCombinationDetail(String combinationId);

    /**
     * 保存标签组合
     *
     * @param tagCombination 组合信息
     * @return 结果
     */
    FrResult<?> saveCombination(TagCombination tagCombination);

    /**
     * 更新标签组合
     *
     * @param tagCombination 组合信息
     * @return 结果
     */
    FrResult<?> updateCombination(TagCombination tagCombination);

    /**
     * 删除标签组合
     *
     * @param id 组合ID
     * @return 结果
     */
    FrResult<?> deleteCombination(String id);

    /**
     * 检查组合编码是否存在
     *
     * @param combinationCode 组合编码
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean checkCombinationCodeExists(String combinationCode, String excludeId);

    /**
     * 更新组合状态
     *
     * @param id 组合ID
     * @param isEnabled 是否启用
     * @return 结果
     */
    FrResult<?> updateCombinationStatus(String id, String isEnabled);

    /**
     * 更新组合使用次数
     *
     * @param combinationId 组合ID
     * @return 结果
     */
    FrResult<?> updateUsageCount(String combinationId);

    /**
     * 查询热门组合
     *
     * @param limit 限制数量
     * @return 组合列表
     */
    List<TagCombination> getPopularCombinations(int limit);

    /**
     * 查询组合使用统计
     *
     * @return 统计信息
     */
    List<Map<String, Object>> getUsageStatistics();
}