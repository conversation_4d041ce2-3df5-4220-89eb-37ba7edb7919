package org.simple.equipment.service;

import org.simple.equipment.dto.ExceptionAnalysisRequest;
import org.simple.equipment.dto.ExceptionAnalysisResponse;
import org.simple.equipment.dto.ExceptionHandlingRequest;
import org.simple.equipment.entity.ExceptionHandlingRecord;

import java.util.List;

/**
 * 智能异常处理服务接口
 * 
 * <AUTHOR>
 */
public interface IntelligentExceptionHandlerService {

    /**
     * 智能分析异常等级
     * 
     * @param request 分析请求
     * @return 分析结果
     */
    ExceptionAnalysisResponse analyzeException(ExceptionAnalysisRequest request);

    /**
     * 处理异常事件
     * 
     * @param request 处理请求
     * @return 处理记录
     */
    ExceptionHandlingRecord handleException(ExceptionHandlingRequest request);

    /**
     * 自动通知相关人员
     * 
     * @param recordId 异常记录ID
     * @return 通知结果
     */
    Boolean sendNotifications(String recordId);

    /**
     * 自动生成维修工单
     * 
     * @param recordId 异常记录ID
     * @return 工单ID
     */
    String generateWorkOrder(String recordId);

    /**
     * 异常升级处理
     * 
     * @param recordId 异常记录ID
     * @return 升级结果
     */
    Boolean escalateException(String recordId);

    /**
     * 获取异常处理进度
     * 
     * @param recordId 异常记录ID
     * @return 处理记录
     */
    ExceptionHandlingRecord getHandlingProgress(String recordId);

    /**
     * 批量处理超时异常
     * 
     * @return 处理数量
     */
    Integer processOverdueExceptions();

    /**
     * 获取设备异常统计
     * 
     * @param equipmentId 设备ID
     * @param days 统计天数
     * @return 异常记录列表
     */
    List<ExceptionHandlingRecord> getEquipmentExceptionHistory(String equipmentId, Integer days);
}