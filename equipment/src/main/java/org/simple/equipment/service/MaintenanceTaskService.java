package org.simple.equipment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.simple.equipment.entity.MaintenanceTask;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 设备保养任务服务接口
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
public interface MaintenanceTaskService extends IService<MaintenanceTask> {

    /**
     * 分页查询保养任务
     *
     * @param page 分页对象
     * @param task 查询条件
     * @return 分页结果
     */
    IPage<MaintenanceTask> selectMaintenanceTaskPage(Page<MaintenanceTask> page, MaintenanceTask task);

    /**
     * 创建保养任务
     *
     * @param task 保养任务
     * @return 是否成功
     */
    boolean createMaintenanceTask(MaintenanceTask task);

    /**
     * 更新保养任务
     *
     * @param task 保养任务
     * @return 是否成功
     */
    boolean updateMaintenanceTask(MaintenanceTask task);

    /**
     * 删除保养任务
     *
     * @param taskId 任务ID
     * @return 是否成功
     */
    boolean deleteMaintenanceTask(String taskId);

    /**
     * 分配保养任务
     *
     * @param taskId 任务ID
     * @param executorId 执行人ID
     * @param assignedBy 分配人ID
     * @return 是否成功
     */
    boolean assignTask(String taskId, String executorId, String assignedBy);

    /**
     * 接受保养任务
     *
     * @param taskId 任务ID
     * @param executorId 执行人ID
     * @return 是否成功
     */
    boolean acceptTask(String taskId, String executorId);

    /**
     * 开始执行保养任务
     *
     * @param taskId 任务ID
     * @param executorId 执行人ID
     * @return 是否成功
     */
    boolean startTask(String taskId, String executorId);

    /**
     * 暂停保养任务
     *
     * @param taskId 任务ID
     * @param executorId 执行人ID
     * @param reason 暂停原因
     * @return 是否成功
     */
    boolean pauseTask(String taskId, String executorId, String reason);

    /**
     * 恢复保养任务
     *
     * @param taskId 任务ID
     * @param executorId 执行人ID
     * @return 是否成功
     */
    boolean resumeTask(String taskId, String executorId);

    /**
     * 完成保养任务
     *
     * @param taskId 任务ID
     * @param executorId 执行人ID
     * @param result 执行结果
     * @return 是否成功
     */
    boolean completeTask(String taskId, String executorId, String result);

    /**
     * 取消保养任务
     *
     * @param taskId 任务ID
     * @param cancelReason 取消原因
     * @param cancelBy 取消人ID
     * @return 是否成功
     */
    boolean cancelTask(String taskId, String cancelReason, String cancelBy);

    /**
     * 更新任务进度
     *
     * @param taskId 任务ID
     * @param percentage 完成百分比
     * @return 是否成功
     */
    boolean updateTaskProgress(String taskId, BigDecimal percentage);

    /**
     * 获取用户的保养任务
     *
     * @param userId 用户ID
     * @param status 任务状态
     * @return 任务列表
     */
    List<MaintenanceTask> getUserMaintenanceTasks(String userId, String status);

    /**
     * 获取部门的保养任务
     *
     * @param deptId 部门ID
     * @param status 任务状态
     * @return 任务列表
     */
    List<MaintenanceTask> getDeptMaintenanceTasks(String deptId, String status);

    /**
     * 获取即将到期的保养任务
     *
     * @param hours 小时数
     * @return 任务列表
     */
    List<MaintenanceTask> getUpcomingTasks(Integer hours);

    /**
     * 获取超时的保养任务
     *
     * @return 任务列表
     */
    List<MaintenanceTask> getOverdueTasks();

    /**
     * 获取保养任务统计信息
     *
     * @param userId 用户ID
     * @param deptId 部门ID
     * @return 统计信息
     */
    Map<String, Object> getTaskStatistics(String userId, String deptId);

    /**
     * 获取任务执行详情
     *
     * @param taskId 任务ID
     * @return 执行详情
     */
    Map<String, Object> getTaskExecutionDetails(String taskId);

    /**
     * 批量分配保养任务
     *
     * @param taskIds 任务ID列表
     * @param executorId 执行人ID
     * @param assignedBy 分配人ID
     * @return 分配结果
     */
    Map<String, Object> batchAssignTasks(List<String> taskIds, String executorId, String assignedBy);

    /**
     * 检查并更新超时任务
     *
     * @return 更新数量
     */
    int checkAndUpdateOverdueTasks();

    /**
     * 生成任务报告
     *
     * @param taskId 任务ID
     * @return 报告数据
     */
    Map<String, Object> generateTaskReport(String taskId);

    /**
     * 导出任务数据
     *
     * @param taskIds 任务ID列表
     * @return 导出结果
     */
    List<Map<String, Object>> exportTaskData(List<String> taskIds);

    /**
     * 获取设备的保养任务
     *
     * @param equipmentId 设备ID
     * @param status 任务状态
     * @return 任务列表
     */
    List<MaintenanceTask> getEquipmentMaintenanceTasks(String equipmentId, String status);

    /**
     * 评价保养任务
     *
     * @param taskId 任务ID
     * @param evaluatedBy 评价人ID
     * @param evaluation 评价内容
     * @param score 评价得分
     * @return 是否成功
     */
    boolean evaluateTask(String taskId, String evaluatedBy, String evaluation, BigDecimal score);

    /**
     * 获取任务的费用统计
     *
     * @param taskId 任务ID
     * @return 费用统计
     */
    Map<String, Object> getTaskCostStatistics(String taskId);

    /**
     * 获取任务的工时统计
     *
     * @param taskId 任务ID
     * @return 工时统计
     */
    Map<String, Object> getTaskHourStatistics(String taskId);

    /**
     * 获取任务的完成情况
     *
     * @param taskId 任务ID
     * @return 完成情况
     */
    Map<String, Object> getTaskCompletionStatus(String taskId);

    /**
     * 获取任务的质量评价
     *
     * @param taskId 任务ID
     * @return 质量评价
     */
    Map<String, Object> getTaskQualityEvaluation(String taskId);

    /**
     * 获取外包任务
     *
     * @param isOutsourced 是否外包
     * @param userId 用户ID
     * @param deptId 部门ID
     * @return 外包任务列表
     */
    List<MaintenanceTask> getOutsourcedTasks(Integer isOutsourced, String userId, String deptId);

    /**
     * 更新任务费用
     *
     * @param taskId 任务ID
     * @param actualCost 实际费用
     * @return 是否成功
     */
    boolean updateTaskCost(String taskId, BigDecimal actualCost);

    /**
     * 更新任务工时
     *
     * @param taskId 任务ID
     * @param actualHours 实际工时
     * @return 是否成功
     */
    boolean updateTaskHours(String taskId, BigDecimal actualHours);

    /**
     * 延期保养任务
     *
     * @param taskId 任务ID
     * @param newEndTime 新结束时间
     * @param delayReason 延期原因
     * @return 是否成功
     */
    boolean delayTask(String taskId, String newEndTime, String delayReason);

    /**
     * 紧急处理保养任务
     *
     * @param taskId 任务ID
     * @param isUrgent 是否紧急
     * @return 是否成功
     */
    boolean setTaskUrgent(String taskId, Integer isUrgent);

    /**
     * 获取任务历史记录
     *
     * @param taskId 任务ID
     * @return 历史记录
     */
    List<Map<String, Object>> getTaskHistory(String taskId);
}