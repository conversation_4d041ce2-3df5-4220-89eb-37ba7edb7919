package org.simple.equipment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.simple.equipment.entity.AssetApproval;

import java.util.List;
import java.util.Map;

/**
 * 设备档案审批流程服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public interface AssetApprovalService extends IService<AssetApproval> {

    /**
     * 查询版本的审批记录
     *
     * @param versionId 版本ID
     * @return 审批记录列表
     */
    List<AssetApproval> getApprovalsByVersionId(String versionId);

    /**
     * 查询待审批的任务
     *
     * @param approver 审批人
     * @return 待审批任务列表
     */
    List<Map<String, Object>> getPendingApprovals(String approver);

    /**
     * 查询审批统计信息
     *
     * @return 统计信息
     */
    Map<String, Object> getApprovalStatistics();
}