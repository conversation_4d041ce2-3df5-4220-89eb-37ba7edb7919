package org.simple.equipment.service;

import org.simple.equipment.dto.NurseWorkspaceData;
import org.simple.equipment.dto.QuickActionConfig;
import org.simple.equipment.entity.NurseQuickAction;
import org.simple.equipment.entity.NurseTaskOverview;
import org.simple.equipment.entity.NurseWorkspaceConfig;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 护士工作台服务接口
 * 
 * <AUTHOR>
 */
public interface NurseWorkspaceService {

    /**
     * 获取护士工作台数据
     * 
     * @param userId 护士用户ID
     * @return 工作台数据
     */
    NurseWorkspaceData getNurseWorkspaceData(String userId);

    /**
     * 获取今日任务概览
     * 
     * @param userId 护士用户ID
     * @return 任务概览
     */
    NurseTaskOverview getTodayTaskOverview(String userId);

    /**
     * 更新任务概览统计
     * 
     * @param userId 护士用户ID
     * @param taskDate 任务日期
     * @return 更新结果
     */
    Boolean updateTaskOverviewStatistics(String userId, LocalDate taskDate);

    /**
     * 获取护士快速操作配置
     * 
     * @param userId 护士用户ID
     * @return 快速操作列表
     */
    List<NurseQuickAction> getQuickActions(String userId);

    /**
     * 配置快速操作
     * 
     * @param userId 护士用户ID
     * @param actions 快速操作配置
     * @return 配置结果
     */
    Boolean configureQuickActions(String userId, List<QuickActionConfig> actions);

    /**
     * 使用快速操作
     * 
     * @param userId 护士用户ID
     * @param actionCode 操作代码
     * @return 操作结果
     */
    Map<String, Object> executeQuickAction(String userId, String actionCode);

    /**
     * 获取护士工作台配置
     * 
     * @param userId 护士用户ID
     * @return 工作台配置
     */
    NurseWorkspaceConfig getWorkspaceConfig(String userId);

    /**
     * 更新工作台配置
     * 
     * @param userId 护士用户ID
     * @param config 配置信息
     * @return 更新结果
     */
    Boolean updateWorkspaceConfig(String userId, Map<String, Object> config);

    /**
     * 获取护士个人统计
     * 
     * @param userId 护士用户ID
     * @param period 统计周期(DAILY/WEEKLY/MONTHLY)
     * @return 统计数据
     */
    Map<String, Object> getPersonalStatistics(String userId, String period);

    /**
     * 获取科室排行榜
     * 
     * @param departmentId 科室ID
     * @param category 排行类别
     * @param period 排行周期
     * @return 排行榜数据
     */
    List<Map<String, Object>> getDepartmentRanking(String departmentId, String category, String period);

    /**
     * 初始化护士工作台
     * 
     * @param userId 护士用户ID
     * @param departmentId 科室ID
     * @return 初始化结果
     */
    Boolean initializeNurseWorkspace(String userId, String departmentId);

    /**
     * 获取护士工作负荷分析
     * 
     * @param userId 护士用户ID
     * @param days 分析天数
     * @return 工作负荷数据
     */
    Map<String, Object> getWorkloadAnalysis(String userId, Integer days);

    /**
     * 语音操作记录
     * 
     * @param userId 护士用户ID
     * @param voiceCommand 语音命令
     * @param result 操作结果
     */
    void recordVoiceOperation(String userId, String voiceCommand, String result);
}