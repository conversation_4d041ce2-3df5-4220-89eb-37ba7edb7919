package org.simple.equipment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.simple.equipment.entity.InspectionApprovalConfig;

import java.util.List;
import java.util.Map;

/**
 * 设备点检审核配置服务接口
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
public interface InspectionApprovalConfigService extends IService<InspectionApprovalConfig> {

    /**
     * 分页查询审核配置
     */
    IPage<InspectionApprovalConfig> pageConfigs(int current, int size, Map<String, Object> params);

    /**
     * 新增审核配置
     */
    boolean addConfig(InspectionApprovalConfig config);

    /**
     * 更新审核配置
     */
    boolean updateConfig(InspectionApprovalConfig config);

    /**
     * 删除审核配置
     */
    boolean deleteConfig(String configId);

    /**
     * 启用配置
     */
    boolean enableConfig(String configId);

    /**
     * 禁用配置
     */
    boolean disableConfig(String configId);

    /**
     * 查询生效的配置
     */
    List<InspectionApprovalConfig> getActiveConfigs();

    /**
     * 根据适用范围查询配置
     */
    InspectionApprovalConfig getConfigByScope(String scopeType, String scopeValue);

    /**
     * 查询匹配的审核配置
     */
    InspectionApprovalConfig getMatchingConfig(String assetId, String deptId, String categoryId);

    /**
     * 查询默认配置
     */
    InspectionApprovalConfig getDefaultConfig();

    /**
     * 检查配置冲突
     */
    List<InspectionApprovalConfig> checkConfigConflicts(String scopeType, String scopeValue, String excludeId);

    /**
     * 复制配置
     */
    boolean copyConfig(String configId, String newConfigName);

    /**
     * 导入配置
     */
    boolean importConfigs(String configData);

    /**
     * 导出配置
     */
    String exportConfigs(List<String> configIds);

    /**
     * 验证流程定义
     */
    boolean validateFlowDefinition(String flowDefinition);

    /**
     * 解析自动审核条件
     */
    Map<String, Object> parseAutoApprovalCondition(String condition);

    /**
     * 解析紧急审核条件
     */
    Map<String, Object> parseUrgentCondition(String condition);

    /**
     * 生成流程图
     */
    Map<String, Object> generateFlowChart(String configId);

    /**
     * 测试配置
     */
    Map<String, Object> testConfig(String configId, Map<String, Object> testData);

    /**
     * 统计配置使用情况
     */
    List<Map<String, Object>> getConfigUsageStats();

    /**
     * 优化配置建议
     */
    List<Map<String, Object>> getConfigOptimizationSuggestions();

    /**
     * 批量更新配置状态
     */
    boolean batchUpdateConfigStatus(List<String> configIds, String status);
}