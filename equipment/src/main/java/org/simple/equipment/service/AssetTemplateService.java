package org.simple.equipment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.simple.base.vo.FrResult;
import org.simple.equipment.entity.AssetTemplate;

import java.util.List;
import java.util.Map;

/**
 * 设备应用模板记录服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public interface AssetTemplateService extends IService<AssetTemplate> {

    /**
     * 根据设备ID查询应用的模板
     *
     * @param assetId 设备ID
     * @return 应用模板列表
     */
    List<AssetTemplate> getTemplatesByAssetId(String assetId);

    /**
     * 根据模板ID查询应用的设备
     *
     * @param templateId 模板ID
     * @return 设备列表
     */
    List<Map<String, Object>> getAssetsByTemplateId(String templateId);

    /**
     * 查询设备的当前模板
     *
     * @param assetId 设备ID
     * @return 当前模板信息
     */
    Map<String, Object> getCurrentTemplate(String assetId);

    /**
     * 查询模板应用历史
     *
     * @param assetId 设备ID
     * @return 应用历史
     */
    List<Map<String, Object>> getApplyHistory(String assetId);

    /**
     * 查询模板应用统计
     *
     * @param templateId 模板ID
     * @return 应用统计
     */
    Map<String, Object> getApplyStatistics(String templateId);

    /**
     * 记录模板应用
     *
     * @param assetId 设备ID
     * @param templateId 模板ID
     * @param applyMode 应用模式
     * @param applyResult 应用结果
     * @return 结果
     */
    FrResult<?> recordTemplateApply(String assetId, String templateId, String applyMode, String applyResult);

    /**
     * 批量记录模板应用
     *
     * @param assetTemplates 应用记录列表
     * @return 结果
     */
    FrResult<?> batchRecordTemplateApply(List<AssetTemplate> assetTemplates);

    /**
     * 取消设备的模板应用
     *
     * @param assetId 设备ID
     * @param templateId 模板ID
     * @return 结果
     */
    FrResult<?> cancelTemplateApply(String assetId, String templateId);

    /**
     * 清除设备的所有模板应用记录
     *
     * @param assetId 设备ID
     * @return 结果
     */
    FrResult<?> clearAssetTemplates(String assetId);
}