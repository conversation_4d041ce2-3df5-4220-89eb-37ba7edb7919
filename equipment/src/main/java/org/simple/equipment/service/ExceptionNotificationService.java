package org.simple.equipment.service;

/**
 * 异常通知服务接口
 * 
 * <AUTHOR>
 */
public interface ExceptionNotificationService {

    /**
     * 发送异常通知
     * 
     * @param exceptionRecordId 异常记录ID
     * @return 发送结果
     */
    Boolean sendExceptionNotifications(String exceptionRecordId);

    /**
     * 发送升级通知
     * 
     * @param exceptionRecordId 异常记录ID
     * @return 发送结果
     */
    Boolean sendEscalationNotification(String exceptionRecordId);

    /**
     * 发送工单通知
     * 
     * @param workOrderId 工单ID
     * @return 发送结果
     */
    Boolean sendWorkOrderNotification(String workOrderId);
}