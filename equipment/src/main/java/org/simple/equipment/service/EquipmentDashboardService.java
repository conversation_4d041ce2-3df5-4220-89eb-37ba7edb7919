package org.simple.equipment.service;

import java.util.Map;

/**
 * 设备管理仪表盘服务接口
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
public interface EquipmentDashboardService {

    /**
     * 获取设备总览统计
     *
     * @return 设备总览数据
     */
    Map<String, Object> getEquipmentOverview();

    /**
     * 获取设备状态分布
     *
     * @return 设备状态分布数据
     */
    Map<String, Object> getEquipmentStatusDistribution();

    /**
     * 获取设备运行情况
     *
     * @return 设备运行情况数据
     */
    Map<String, Object> getEquipmentRunningStatus();

    /**
     * 获取任务概览
     *
     * @return 任务概览数据
     */
    Map<String, Object> getTaskOverview();

    /**
     * 获取维修任务统计
     *
     * @return 维修任务统计数据
     */
    Map<String, Object> getRepairTaskStatistics();

    /**
     * 获取保养任务统计
     *
     * @return 保养任务统计数据
     */
    Map<String, Object> getMaintenanceTaskStatistics();

    /**
     * 获取点检任务统计
     *
     * @return 点检任务统计数据
     */
    Map<String, Object> getInspectionTaskStatistics();

    /**
     * 获取巡检任务统计
     *
     * @return 巡检任务统计数据
     */
    Map<String, Object> getPatrolTaskStatistics();

    /**
     * 获取设备价值统计
     *
     * @return 设备价值统计数据
     */
    Map<String, Object> getEquipmentValueStatistics();

    /**
     * 获取设备类型分布
     *
     * @return 设备类型分布数据
     */
    Map<String, Object> getEquipmentTypeDistribution();

    /**
     * 获取部门设备分布
     *
     * @return 部门设备分布数据
     */
    Map<String, Object> getDepartmentEquipmentDistribution();

    /**
     * 获取设备健康度评估
     *
     * @return 设备健康度评估数据
     */
    Map<String, Object> getEquipmentHealthAssessment();

    /**
     * 获取故障率统计
     *
     * @return 故障率统计数据
     */
    Map<String, Object> getFailureRateStatistics();

    /**
     * 获取设备利用率统计
     *
     * @return 设备利用率统计数据
     */
    Map<String, Object> getEquipmentUtilizationStatistics();

    /**
     * 获取近期告警信息
     *
     * @return 近期告警信息
     */
    Map<String, Object> getRecentAlerts();

    /**
     * 获取待处理事项
     *
     * @return 待处理事项数据
     */
    Map<String, Object> getPendingItems();

    /**
     * 获取设备趋势分析
     *
     * @param days 统计天数
     * @return 设备趋势分析数据
     */
    Map<String, Object> getEquipmentTrendAnalysis(Integer days);

    /**
     * 获取成本统计
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 成本统计数据
     */
    Map<String, Object> getCostStatistics(String startDate, String endDate);

    /**
     * 获取工作效率统计
     *
     * @return 工作效率统计数据
     */
    Map<String, Object> getWorkEfficiencyStatistics();

    /**
     * 获取设备实时监控数据
     *
     * @return 设备实时监控数据
     */
    Map<String, Object> getEquipmentRealTimeMonitoring();

    /**
     * 获取KPI指标
     *
     * @return KPI指标数据
     */
    Map<String, Object> getKPIIndicators();
}