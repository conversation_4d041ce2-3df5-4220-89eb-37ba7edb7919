package org.simple.equipment.service;

import org.simple.equipment.dto.QrCodeGenerateRequest;
import org.simple.equipment.dto.QrCodeScanRequest;
import org.simple.equipment.dto.QrCodeScanResponse;
import org.simple.equipment.entity.EquipmentQrCode;

/**
 * 设备二维码服务接口
 * 
 * <AUTHOR>
 */
public interface EquipmentQrCodeService {

    /**
     * 生成设备二维码
     * 
     * @param request 生成请求
     * @return 二维码信息
     */
    EquipmentQrCode generateEquipmentQrCode(QrCodeGenerateRequest request);

    /**
     * 扫描二维码并获取设备信息
     * 
     * @param request 扫描请求
     * @return 扫描结果
     */
    QrCodeScanResponse scanQrCode(QrCodeScanRequest request);

    /**
     * 根据设备ID获取二维码
     * 
     * @param equipmentId 设备ID
     * @return 二维码信息
     */
    EquipmentQrCode getQrCodeByEquipmentId(String equipmentId);

    /**
     * 更新二维码扫描统计
     * 
     * @param qrCodeId 二维码ID
     * @param scannerId 扫描人ID
     */
    void updateScanStatistics(String qrCodeId, String scannerId);

    /**
     * 使二维码失效
     * 
     * @param equipmentId 设备ID
     */
    void deactivateQrCode(String equipmentId);

    /**
     * 重新生成二维码
     * 
     * @param equipmentId 设备ID
     * @return 新的二维码信息
     */
    EquipmentQrCode regenerateQrCode(String equipmentId);
}