package org.simple.equipment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.simple.equipment.entity.InspectionApproval;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 设备点检审核记录服务接口
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
public interface InspectionApprovalService extends IService<InspectionApproval> {

    /**
     * 分页查询审核记录
     */
    IPage<InspectionApproval> pageApprovals(int current, int size, Map<String, Object> params);

    /**
     * 提交审核
     */
    boolean submitApproval(String inspectionTaskId, String submitUserId);

    /**
     * 审核通过
     */
    boolean approvePass(String approvalId, String approverId, String comment);

    /**
     * 审核驳回
     */
    boolean approveReject(String approvalId, String approverId, String comment, String reason);

    /**
     * 审核退回
     */
    boolean approveReturn(String approvalId, String approverId, String comment, String reason);

    /**
     * 撤回审核
     */
    boolean withdrawApproval(String approvalId, String userId);

    /**
     * 批量审核
     */
    boolean batchApprove(List<String> approvalIds, String approverId, String result, String comment);

    /**
     * 查询待审核记录
     */
    List<InspectionApproval> getPendingApprovals(String approverId);

    /**
     * 查询已审核记录
     */
    List<InspectionApproval> getApprovedRecords(String approverId);

    /**
     * 根据任务ID查询审核记录
     */
    InspectionApproval getByTaskId(String inspectionTaskId);

    /**
     * 查询审核历史
     */
    List<InspectionApproval> getApprovalHistory(String assetId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计审核数据
     */
    Map<String, Object> getApprovalStatistics(Map<String, Object> params);

    /**
     * 计算质量得分
     */
    int calculateQualityScore(String inspectionTaskId);

    /**
     * 自动审核检查
     */
    boolean checkAutoApproval(String approvalId);

    /**
     * 处理超时审核
     */
    void handleTimeoutApprovals();

    /**
     * 发送审核通知
     */
    void sendApprovalNotification(String approvalId, String notificationType);

    /**
     * 导出审核记录
     */
    void exportApprovalRecords(Map<String, Object> params, String exportType);

    /**
     * 查询异常审核记录
     */
    List<InspectionApproval> getAbnormalApprovals();

    /**
     * 统计质量等级分布
     */
    List<Map<String, Object>> getQualityDistribution(Map<String, Object> params);

    /**
     * 查询审核工作量统计
     */
    List<Map<String, Object>> getApprovalWorkload(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 生成审核报告
     */
    Map<String, Object> generateApprovalReport(Map<String, Object> params);
}