package org.simple.equipment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.simple.base.dto.mybatis.Page;
import org.simple.base.vo.FrResult;
import org.simple.equipment.dto.AssetDto;
import org.simple.equipment.entity.Asset;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 设备档案服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public interface AssetService extends IService<Asset> {

    /**
     * 分页查询设备档案
     *
     * @param page  分页参数
     * @param asset 查询条件
     * @return 设备档案列表
     */
    IPage<AssetDto> listAssets(Page<AssetDto> page, Asset asset);

    /**
     * 根据ID查询设备档案详情
     *
     * @param id 设备ID
     * @return 设备档案详情
     */
    AssetDto getAssetById(String id);

    /**
     * 新增设备档案
     *
     * @param asset 设备档案信息
     * @return 操作结果
     */
    FrResult<?> saveAsset(Asset asset);

    /**
     * 修改设备档案
     *
     * @param asset 设备档案信息
     * @return 操作结果
     */
    FrResult<?> updateAsset(Asset asset);

    /**
     * 删除设备档案
     *
     * @param id 设备ID
     * @return 操作结果
     */
    FrResult<?> deleteAsset(String id);

    /**
     * 批量删除设备档案
     *
     * @param ids 设备ID列表
     * @return 操作结果
     */
    FrResult<?> batchDeleteAssets(List<String> ids);

    /**
     * 生成设备编码
     *
     * @param categoryCode 分类编码
     * @return 设备编码
     */
    String generateEquipmentCode(String categoryCode);

    /**
     * 生成设备二维码
     *
     * @param assetId 设备ID
     * @return 二维码内容
     */
    String generateQrCode(String assetId);

    /**
     * 批量导入设备档案
     *
     * @param file Excel文件
     * @return 导入结果
     */
    FrResult<?> importAssets(MultipartFile file);

    /**
     * 导出设备档案
     *
     * @param asset 查询条件
     * @return Excel文件字节数组
     */
    byte[] exportAssets(Asset asset);

    /**
     * 查询科室下的设备列表
     *
     * @param departmentId 科室ID
     * @return 设备列表
     */
    List<AssetDto> getAssetsByDepartment(String departmentId);

    /**
     * 更新设备状态
     *
     * @param assetId 设备ID
     * @param status  新状态
     * @return 操作结果
     */
    FrResult<?> updateAssetStatus(String assetId, String status);

    /**
     * 获取设备状态统计
     *
     * @return 统计结果
     */
    List<Map<String, Object>> getStatusStatistics();

    /**
     * 设备调拨
     *
     * @param assetId        设备ID
     * @param newDepartmentId 新科室ID
     * @param newLocationId   新位置ID
     * @param reason         调拨原因
     * @return 操作结果
     */
    FrResult<?> transferAsset(String assetId, String newDepartmentId, 
                            String newLocationId, String reason);
}