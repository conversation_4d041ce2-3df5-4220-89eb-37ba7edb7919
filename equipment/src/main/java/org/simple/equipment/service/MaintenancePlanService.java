package org.simple.equipment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.simple.equipment.entity.MaintenancePlan;

import java.util.List;
import java.util.Map;

/**
 * 设备保养计划服务接口
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
public interface MaintenancePlanService extends IService<MaintenancePlan> {

    /**
     * 分页查询保养计划
     *
     * @param page 分页对象
     * @param plan 查询条件
     * @return 分页结果
     */
    IPage<MaintenancePlan> selectMaintenancePlanPage(Page<MaintenancePlan> page, MaintenancePlan plan);

    /**
     * 创建保养计划
     *
     * @param plan 保养计划
     * @return 是否成功
     */
    boolean createMaintenancePlan(MaintenancePlan plan);

    /**
     * 更新保养计划
     *
     * @param plan 保养计划
     * @return 是否成功
     */
    boolean updateMaintenancePlan(MaintenancePlan plan);

    /**
     * 删除保养计划
     *
     * @param planId 计划ID
     * @return 是否成功
     */
    boolean deleteMaintenancePlan(String planId);

    /**
     * 更新计划状态
     *
     * @param planId 计划ID
     * @param status 状态
     * @return 是否成功
     */
    boolean updatePlanStatus(String planId, Integer status);

    /**
     * 复制保养计划
     *
     * @param planId 计划ID
     * @param newPlanName 新计划名称
     * @return 新计划ID
     */
    String copyMaintenancePlan(String planId, String newPlanName);

    /**
     * 根据计划生成保养任务
     *
     * @param planId 计划ID
     * @return 生成的任务数量
     */
    int generateTasksByPlan(String planId);

    /**
     * 批量生成保养任务
     *
     * @param planIds 计划ID列表
     * @return 生成结果
     */
    Map<String, Object> batchGenerateTasks(List<String> planIds);

    /**
     * 获取保养计划统计信息
     *
     * @return 统计信息
     */
    Map<String, Object> getMaintenancePlanStatistics();

    /**
     * 获取即将到期的保养计划
     *
     * @param days 天数
     * @return 计划列表
     */
    List<MaintenancePlan> getExpiringPlans(Integer days);

    /**
     * 验证计划配置
     *
     * @param plan 保养计划
     * @return 验证结果
     */
    Map<String, Object> validatePlanConfiguration(MaintenancePlan plan);

    /**
     * 获取计划执行情况
     *
     * @param planId 计划ID
     * @param days 统计天数
     * @return 执行情况
     */
    Map<String, Object> getPlanExecutionStatus(String planId, Integer days);

    /**
     * 导入保养计划
     *
     * @param planData 计划数据
     * @return 导入结果
     */
    Map<String, Object> importMaintenancePlans(List<Map<String, Object>> planData);

    /**
     * 导出保养计划
     *
     * @param planIds 计划ID列表
     * @return 导出结果
     */
    List<Map<String, Object>> exportMaintenancePlans(List<String> planIds);

    /**
     * 获取设备的保养计划
     *
     * @param equipmentId 设备ID
     * @return 计划列表
     */
    List<MaintenancePlan> getEquipmentMaintenancePlans(String equipmentId);

    /**
     * 获取部门的保养计划
     *
     * @param deptId 部门ID
     * @return 计划列表
     */
    List<MaintenancePlan> getDeptMaintenancePlans(String deptId);

    /**
     * 计算下次执行时间
     *
     * @param plan 保养计划
     * @return 下次执行时间
     */
    String calculateNextExecutionTime(MaintenancePlan plan);

    /**
     * 更新计划执行次数
     *
     * @param planId 计划ID
     * @return 是否成功
     */
    boolean updatePlanExecutionCount(String planId);

    /**
     * 获取计划的任务统计
     *
     * @param planId 计划ID
     * @param days 统计天数
     * @return 任务统计
     */
    Map<String, Object> getPlanTaskStatistics(String planId, Integer days);

    /**
     * 获取计划的费用统计
     *
     * @param planId 计划ID
     * @param days 统计天数
     * @return 费用统计
     */
    Map<String, Object> getPlanCostStatistics(String planId, Integer days);

    /**
     * 启用/禁用保养计划
     *
     * @param planId 计划ID
     * @param enabled 是否启用
     * @return 是否成功
     */
    boolean enableMaintenancePlan(String planId, Integer enabled);

    /**
     * 获取保养计划详情
     *
     * @param planId 计划ID
     * @return 计划详情
     */
    Map<String, Object> getMaintenancePlanDetails(String planId);

    /**
     * 检查计划冲突
     *
     * @param plan 保养计划
     * @return 冲突信息
     */
    Map<String, Object> checkPlanConflict(MaintenancePlan plan);

    /**
     * 自动生成保养任务（定时任务）
     *
     * @return 生成结果
     */
    Map<String, Object> autoGenerateMaintenanceTasks();
}