package org.simple.equipment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.simple.equipment.entity.InspectionApprovalFlow;

import java.util.List;
import java.util.Map;

/**
 * 设备点检审核流程记录服务接口
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
public interface InspectionApprovalFlowService extends IService<InspectionApprovalFlow> {

    /**
     * 初始化审核流程
     */
    boolean initApprovalFlow(String approvalId, String configId);

    /**
     * 启动审核流程
     */
    boolean startApprovalFlow(String approvalId);

    /**
     * 处理审核节点
     */
    boolean processApprovalNode(String flowId, String approverId, String result, String comment);

    /**
     * 跳转到下一个节点
     */
    boolean moveToNextNode(String approvalId, String currentFlowId);

    /**
     * 查询审核流程记录
     */
    List<InspectionApprovalFlow> getApprovalFlowByApprovalId(String approvalId);

    /**
     * 查询当前审核节点
     */
    InspectionApprovalFlow getCurrentApprovalNode(String approvalId);

    /**
     * 查询下一个审核节点
     */
    InspectionApprovalFlow getNextApprovalNode(String approvalId, Integer currentNode);

    /**
     * 查询待处理的流程节点
     */
    List<InspectionApprovalFlow> getPendingFlowNodes(String approverId);

    /**
     * 查询审核路径
     */
    List<InspectionApprovalFlow> getApprovalPath(String inspectionTaskId);

    /**
     * 委托审核
     */
    boolean delegateApproval(String flowId, String delegateFrom, String delegateTo, String reason);

    /**
     * 取消委托
     */
    boolean cancelDelegate(String flowId, String delegateFrom);

    /**
     * 跳过审核节点
     */
    boolean skipApprovalNode(String flowId, String reason);

    /**
     * 插入紧急审核节点
     */
    boolean insertUrgentNode(String approvalId, String urgentApproverId, String reason);

    /**
     * 计算节点处理时长
     */
    void calculateProcessDuration(String flowId);

    /**
     * 统计审核流程效率
     */
    List<Map<String, Object>> getFlowEfficiencyStats(Map<String, Object> params);

    /**
     * 查询代理审核记录
     */
    List<InspectionApprovalFlow> getDelegateRecords(String delegateFrom);

    /**
     * 检查流程完整性
     */
    boolean validateFlowIntegrity(String approvalId);

    /**
     * 重置审核流程
     */
    boolean resetApprovalFlow(String approvalId, String reason);

    /**
     * 自动推进流程
     */
    void autoAdvanceFlow();

    /**
     * 流程图可视化数据
     */
    Map<String, Object> getFlowVisualizationData(String approvalId);
}