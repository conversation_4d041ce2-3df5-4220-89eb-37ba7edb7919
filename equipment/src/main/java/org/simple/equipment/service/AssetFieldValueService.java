package org.simple.equipment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.simple.base.vo.FrResult;
import org.simple.equipment.entity.AssetFieldValue;

import java.util.List;
import java.util.Map;

/**
 * 设备自定义字段值服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public interface AssetFieldValueService extends IService<AssetFieldValue> {

    /**
     * 根据设备ID查询字段值
     *
     * @param assetId 设备ID
     * @return 字段值列表
     */
    List<AssetFieldValue> getValuesByAssetId(String assetId);

    /**
     * 查询设备的自定义字段值（包含字段配置信息）
     *
     * @param assetId 设备ID
     * @return 字段值Map
     */
    List<Map<String, Object>> getAssetFieldValues(String assetId);

    /**
     * 保存设备的自定义字段值
     *
     * @param assetId 设备ID
     * @param fieldValues 字段值Map
     * @return 结果
     */
    FrResult<?> saveAssetFieldValues(String assetId, Map<String, String> fieldValues);

    /**
     * 批量保存字段值
     *
     * @param fieldValues 字段值列表
     * @return 结果
     */
    FrResult<?> batchSaveFieldValues(List<AssetFieldValue> fieldValues);

    /**
     * 删除设备的所有字段值
     *
     * @param assetId 设备ID
     * @return 结果
     */
    FrResult<?> deleteAssetFieldValues(String assetId);

    /**
     * 删除字段相关的所有值
     *
     * @param fieldId 字段ID
     * @return 结果
     */
    FrResult<?> deleteFieldValues(String fieldId);

    /**
     * 复制设备的字段值
     *
     * @param fromAssetId 源设备ID
     * @param toAssetId 目标设备ID
     * @return 结果
     */
    FrResult<?> copyAssetFieldValues(String fromAssetId, String toAssetId);

    /**
     * 验证字段值
     *
     * @param fieldId 字段ID
     * @param fieldValue 字段值
     * @return 验证结果
     */
    FrResult<?> validateFieldValue(String fieldId, String fieldValue);
}