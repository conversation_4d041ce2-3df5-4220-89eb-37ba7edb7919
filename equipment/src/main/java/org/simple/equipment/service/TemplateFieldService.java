package org.simple.equipment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.simple.base.vo.FrResult;
import org.simple.equipment.entity.TemplateField;

import java.util.List;
import java.util.Map;

/**
 * 模板字段关联服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public interface TemplateFieldService extends IService<TemplateField> {

    /**
     * 根据模板ID查询字段列表
     *
     * @param templateId 模板ID
     * @return 字段列表
     */
    List<TemplateField> getFieldsByTemplateId(String templateId);

    /**
     * 查询模板的字段信息（包含字段详情）
     *
     * @param templateId 模板ID
     * @return 字段信息列表
     */
    List<Map<String, Object>> getTemplateFieldDetails(String templateId);

    /**
     * 根据字段ID查询使用该字段的模板
     *
     * @param fieldId 字段ID
     * @return 模板列表
     */
    List<Map<String, Object>> getTemplatesByFieldId(String fieldId);

    /**
     * 保存模板字段关联
     *
     * @param templateId 模板ID
     * @param fieldIds 字段ID列表
     * @return 结果
     */
    FrResult<?> saveTemplateFields(String templateId, List<String> fieldIds);

    /**
     * 更新模板字段关联
     *
     * @param templateId 模板ID
     * @param fieldIds 字段ID列表
     * @return 结果
     */
    FrResult<?> updateTemplateFields(String templateId, List<String> fieldIds);

    /**
     * 删除模板的所有字段关联
     *
     * @param templateId 模板ID
     * @return 结果
     */
    FrResult<?> deleteTemplateFields(String templateId);

    /**
     * 批量保存模板字段关联
     *
     * @param templateFields 模板字段列表
     * @return 结果
     */
    FrResult<?> batchSaveTemplateFields(List<TemplateField> templateFields);

    /**
     * 添加字段到模板
     *
     * @param templateId 模板ID
     * @param fieldId 字段ID
     * @param fieldConfig 字段配置
     * @return 结果
     */
    FrResult<?> addFieldToTemplate(String templateId, String fieldId, Map<String, Object> fieldConfig);

    /**
     * 从模板移除字段
     *
     * @param templateId 模板ID
     * @param fieldId 字段ID
     * @return 结果
     */
    FrResult<?> removeFieldFromTemplate(String templateId, String fieldId);

    /**
     * 更新模板字段配置
     *
     * @param templateId 模板ID
     * @param fieldId 字段ID
     * @param fieldConfig 字段配置
     * @return 结果
     */
    FrResult<?> updateTemplateFieldConfig(String templateId, String fieldId, Map<String, Object> fieldConfig);

    /**
     * 查询模板字段数量
     *
     * @param templateId 模板ID
     * @return 字段数量
     */
    int getFieldCount(String templateId);
}