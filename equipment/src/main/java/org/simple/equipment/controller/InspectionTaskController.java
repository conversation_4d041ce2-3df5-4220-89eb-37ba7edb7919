package org.simple.equipment.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.simple.base.dto.PageResult;
import org.simple.equipment.entity.InspectionTask;
import org.simple.equipment.service.InspectionTaskService;

import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;

/**
 * InspectionTask Controller
 *
 * <AUTHOR> System
 * @date 2025-08-16
 */
@RestController
@RequestMapping("/equipment/inspection")
public class InspectionTaskController {

    @Autowired
    private InspectionTaskService inspectiontaskService;

    /**
     * 获取巡检任务列表
     */
    @GetMapping("/list")
    public PageResult<InspectionTask> list(@RequestParam(defaultValue = "1") Integer current,
                                        @RequestParam(defaultValue = "10") Integer size) {
        Page<InspectionTask> page = new Page<>(current, size);
        Page<InspectionTask> result = inspectiontaskService.page(page);
        return PageResult.<InspectionTask>builder()
                .records(result.getRecords())
                .total(result.getTotal())
                .current(result.getCurrent())
                .size(result.getSize())
                .build();
    }

    /**
     * 创建巡检任务
     */
    @PostMapping
    public boolean create(@Valid @RequestBody InspectionTask inspectiontask) {
        return inspectiontaskService.save(inspectiontask);
    }

    /**
     * 执行巡检
     */
    @PutMapping("/{id}")
    public boolean update(@PathVariable String id, @Valid @RequestBody InspectionTask inspectiontask) {
        inspectiontask.setId(id);
        return inspectiontaskService.updateById(inspectiontask);
    }
}