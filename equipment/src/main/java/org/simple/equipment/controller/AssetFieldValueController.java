package org.simple.equipment.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.simple.base.vo.FrResult;
import org.simple.equipment.entity.AssetFieldValue;
import org.simple.equipment.service.AssetFieldValueService;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 设备自定义字段值控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/equipment/assetFieldValue")
@Tag(name = "设备自定义字段值管理", description = "设备自定义字段值管理")
public class AssetFieldValueController {

    @Resource
    private AssetFieldValueService assetFieldValueService;

    /**
     * 查询设备的自定义字段值
     */
    @GetMapping("/list/{assetId}")
    @SaCheckPermission("equipment:asset:query")
    @Operation(summary = "查询设备的自定义字段值")
    public FrResult<List<AssetFieldValue>> list(@PathVariable String assetId) {
        List<AssetFieldValue> list = assetFieldValueService.getValuesByAssetId(assetId);
        return FrResult.success(list);
    }

    /**
     * 查询设备的自定义字段值（包含字段配置信息）
     */
    @GetMapping("/detail/{assetId}")
    @SaCheckPermission("equipment:asset:query")
    @Operation(summary = "查询设备的自定义字段值详情")
    public FrResult<List<Map<String, Object>>> detail(@PathVariable String assetId) {
        List<Map<String, Object>> list = assetFieldValueService.getAssetFieldValues(assetId);
        return FrResult.success(list);
    }

    /**
     * 保存设备的自定义字段值
     */
    @PostMapping("/save/{assetId}")
    @SaCheckPermission("equipment:asset:edit")
    @Operation(summary = "保存设备的自定义字段值")
    public FrResult<?> save(@PathVariable String assetId,
                           @RequestBody Map<String, String> fieldValues) {
        return assetFieldValueService.saveAssetFieldValues(assetId, fieldValues);
    }

    /**
     * 批量保存字段值
     */
    @PostMapping("/batchSave")
    @SaCheckPermission("equipment:asset:edit")
    @Operation(summary = "批量保存字段值")
    public FrResult<?> batchSave(@RequestBody List<AssetFieldValue> fieldValues) {
        return assetFieldValueService.batchSaveFieldValues(fieldValues);
    }

    /**
     * 删除设备的所有字段值
     */
    @DeleteMapping("/del/{assetId}")
    @SaCheckPermission("equipment:asset:del")
    @Operation(summary = "删除设备的所有字段值")
    public FrResult<?> delete(@PathVariable String assetId) {
        return assetFieldValueService.deleteAssetFieldValues(assetId);
    }

    /**
     * 复制设备的字段值
     */
    @PostMapping("/copy")
    @SaCheckPermission("equipment:asset:edit")
    @Operation(summary = "复制设备的字段值")
    public FrResult<?> copy(@RequestParam String fromAssetId,
                           @RequestParam String toAssetId) {
        return assetFieldValueService.copyAssetFieldValues(fromAssetId, toAssetId);
    }

    /**
     * 验证字段值
     */
    @PostMapping("/validate")
    @SaCheckPermission("equipment:asset:edit")
    @Operation(summary = "验证字段值")
    public FrResult<?> validate(@RequestParam String fieldId,
                               @RequestParam String fieldValue) {
        return assetFieldValueService.validateFieldValue(fieldId, fieldValue);
    }
}