package org.simple.equipment.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.simple.base.vo.FrResult;
import org.simple.equipment.service.EquipmentDashboardService;

import java.util.Map;

/**
 * 设备管理仪表盘控制器
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
@Slf4j
@RestController
@RequestMapping("/equipment/dashboard")
@Tag(name = "设备管理仪表盘", description = "设备管理仪表盘接口")
public class EquipmentDashboardController {

    @Autowired
    private EquipmentDashboardService dashboardService;

    @GetMapping("/overview")
    @Operation(summary = "获取设备总览统计")
    public FrResult<Map<String, Object>> getEquipmentOverview() {
        Map<String, Object> overview = dashboardService.getEquipmentOverview();
        return FrResult.success(overview);
    }

    @GetMapping("/status-distribution")
    @Operation(summary = "获取设备状态分布")
    public FrResult<Map<String, Object>> getEquipmentStatusDistribution() {
        Map<String, Object> distribution = dashboardService.getEquipmentStatusDistribution();
        return FrResult.success(distribution);
    }

    @GetMapping("/running-status")
    @Operation(summary = "获取设备运行情况")
    public FrResult<Map<String, Object>> getEquipmentRunningStatus() {
        Map<String, Object> runningStatus = dashboardService.getEquipmentRunningStatus();
        return FrResult.success(runningStatus);
    }

    @GetMapping("/task-overview")
    @Operation(summary = "获取任务概览")
    public FrResult<Map<String, Object>> getTaskOverview() {
        Map<String, Object> taskOverview = dashboardService.getTaskOverview();
        return FrResult.success(taskOverview);
    }

    @GetMapping("/repair-statistics")
    @Operation(summary = "获取维修任务统计")
    public FrResult<Map<String, Object>> getRepairTaskStatistics() {
        Map<String, Object> repairStats = dashboardService.getRepairTaskStatistics();
        return FrResult.success(repairStats);
    }

    @GetMapping("/maintenance-statistics")
    @Operation(summary = "获取保养任务统计")
    public FrResult<Map<String, Object>> getMaintenanceTaskStatistics() {
        Map<String, Object> maintenanceStats = dashboardService.getMaintenanceTaskStatistics();
        return FrResult.success(maintenanceStats);
    }

    @GetMapping("/inspection-statistics")
    @Operation(summary = "获取点检任务统计")
    public FrResult<Map<String, Object>> getInspectionTaskStatistics() {
        Map<String, Object> inspectionStats = dashboardService.getInspectionTaskStatistics();
        return FrResult.success(inspectionStats);
    }

    @GetMapping("/patrol-statistics")
    @Operation(summary = "获取巡检任务统计")
    public FrResult<Map<String, Object>> getPatrolTaskStatistics() {
        Map<String, Object> patrolStats = dashboardService.getPatrolTaskStatistics();
        return FrResult.success(patrolStats);
    }

    @GetMapping("/value-statistics")
    @Operation(summary = "获取设备价值统计")
    public FrResult<Map<String, Object>> getEquipmentValueStatistics() {
        Map<String, Object> valueStats = dashboardService.getEquipmentValueStatistics();
        return FrResult.success(valueStats);
    }

    @GetMapping("/type-distribution")
    @Operation(summary = "获取设备类型分布")
    public FrResult<Map<String, Object>> getEquipmentTypeDistribution() {
        Map<String, Object> typeDistribution = dashboardService.getEquipmentTypeDistribution();
        return FrResult.success(typeDistribution);
    }

    @GetMapping("/department-distribution")
    @Operation(summary = "获取部门设备分布")
    public FrResult<Map<String, Object>> getDepartmentEquipmentDistribution() {
        Map<String, Object> deptDistribution = dashboardService.getDepartmentEquipmentDistribution();
        return FrResult.success(deptDistribution);
    }

    @GetMapping("/health-assessment")
    @Operation(summary = "获取设备健康度评估")
    public FrResult<Map<String, Object>> getEquipmentHealthAssessment() {
        Map<String, Object> healthAssessment = dashboardService.getEquipmentHealthAssessment();
        return FrResult.success(healthAssessment);
    }

    @GetMapping("/failure-rate")
    @Operation(summary = "获取故障率统计")
    public FrResult<Map<String, Object>> getFailureRateStatistics() {
        Map<String, Object> failureStats = dashboardService.getFailureRateStatistics();
        return FrResult.success(failureStats);
    }

    @GetMapping("/utilization")
    @Operation(summary = "获取设备利用率统计")
    public FrResult<Map<String, Object>> getEquipmentUtilizationStatistics() {
        Map<String, Object> utilizationStats = dashboardService.getEquipmentUtilizationStatistics();
        return FrResult.success(utilizationStats);
    }

    @GetMapping("/recent-alerts")
    @Operation(summary = "获取近期告警信息")
    public FrResult<Map<String, Object>> getRecentAlerts() {
        Map<String, Object> alerts = dashboardService.getRecentAlerts();
        return FrResult.success(alerts);
    }

    @GetMapping("/pending-items")
    @Operation(summary = "获取待处理事项")
    public FrResult<Map<String, Object>> getPendingItems() {
        Map<String, Object> pendingItems = dashboardService.getPendingItems();
        return FrResult.success(pendingItems);
    }

    @GetMapping("/trend-analysis")
    @Operation(summary = "获取设备趋势分析")
    public FrResult<Map<String, Object>> getEquipmentTrendAnalysis(
            @Parameter(description = "统计天数") @RequestParam(defaultValue = "30") Integer days) {
        Map<String, Object> trendAnalysis = dashboardService.getEquipmentTrendAnalysis(days);
        return FrResult.success(trendAnalysis);
    }

    @GetMapping("/cost-statistics")
    @Operation(summary = "获取成本统计")
    public FrResult<Map<String, Object>> getCostStatistics(
            @Parameter(description = "开始日期") @RequestParam(required = false) String startDate,
            @Parameter(description = "结束日期") @RequestParam(required = false) String endDate) {
        Map<String, Object> costStats = dashboardService.getCostStatistics(startDate, endDate);
        return FrResult.success(costStats);
    }

    @GetMapping("/work-efficiency")
    @Operation(summary = "获取工作效率统计")
    public FrResult<Map<String, Object>> getWorkEfficiencyStatistics() {
        Map<String, Object> efficiencyStats = dashboardService.getWorkEfficiencyStatistics();
        return FrResult.success(efficiencyStats);
    }

    @GetMapping("/real-time-monitoring")
    @Operation(summary = "获取设备实时监控数据")
    public FrResult<Map<String, Object>> getEquipmentRealTimeMonitoring() {
        Map<String, Object> realTimeData = dashboardService.getEquipmentRealTimeMonitoring();
        return FrResult.success(realTimeData);
    }

    @GetMapping("/kpi-indicators")
    @Operation(summary = "获取KPI指标")
    public FrResult<Map<String, Object>> getKPIIndicators() {
        Map<String, Object> kpiData = dashboardService.getKPIIndicators();
        return FrResult.success(kpiData);
    }

    @GetMapping("/comprehensive-data")
    @Operation(summary = "获取综合仪表盘数据")
    public FrResult<Map<String, Object>> getComprehensiveDashboardData() {
        Map<String, Object> comprehensiveData = new java.util.HashMap<>();
        
        try {
            // 设备概览
            comprehensiveData.put("equipmentOverview", dashboardService.getEquipmentOverview());
            
            // 任务概览
            comprehensiveData.put("taskOverview", dashboardService.getTaskOverview());
            
            // 设备状态分布
            comprehensiveData.put("statusDistribution", dashboardService.getEquipmentStatusDistribution());
            
            // 近期告警
            comprehensiveData.put("recentAlerts", dashboardService.getRecentAlerts());
            
            // 待处理事项
            comprehensiveData.put("pendingItems", dashboardService.getPendingItems());
            
            // KPI指标
            comprehensiveData.put("kpiIndicators", dashboardService.getKPIIndicators());
            
            // 实时监控
            comprehensiveData.put("realTimeMonitoring", dashboardService.getEquipmentRealTimeMonitoring());
            
            // 设备健康度
            comprehensiveData.put("healthAssessment", dashboardService.getEquipmentHealthAssessment());
            
        } catch (Exception e) {
            log.error("获取综合仪表盘数据失败", e);
            return FrResult.failed("获取仪表盘数据失败");
        }
        
        return FrResult.success(comprehensiveData);
    }
}