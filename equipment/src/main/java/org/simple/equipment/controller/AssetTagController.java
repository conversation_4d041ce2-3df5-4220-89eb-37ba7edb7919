package org.simple.equipment.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.simple.base.vo.FrResult;
import org.simple.equipment.entity.AssetTag;
import org.simple.equipment.service.AssetTagService;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 设备标签关联控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/equipment/assetTag")
@Tag(name = "设备标签关联管理", description = "设备标签关联管理")
public class AssetTagController {

    @Resource
    private AssetTagService assetTagService;

    /**
     * 查询设备的标签
     */
    @GetMapping("/list/{assetId}")
    @SaCheckPermission("equipment:asset:query")
    @Operation(summary = "查询设备的标签")
    public FrResult<List<AssetTag>> list(@PathVariable String assetId) {
        List<AssetTag> list = assetTagService.getTagsByAssetId(assetId);
        return FrResult.success(list);
    }

    /**
     * 查询设备的标签详情
     */
    @GetMapping("/detail/{assetId}")
    @SaCheckPermission("equipment:asset:query")
    @Operation(summary = "查询设备的标签详情")
    public FrResult<List<Map<String, Object>>> detail(@PathVariable String assetId) {
        List<Map<String, Object>> list = assetTagService.getAssetTagDetails(assetId);
        return FrResult.success(list);
    }

    /**
     * 根据标签查询设备列表
     */
    @GetMapping("/assetsByTags")
    @SaCheckPermission("equipment:asset:query")
    @Operation(summary = "根据标签查询设备列表")
    public FrResult<List<Map<String, Object>>> assetsByTags(@RequestParam List<String> tagIds) {
        List<Map<String, Object>> list = assetTagService.getAssetsByTags(tagIds);
        return FrResult.success(list);
    }

    /**
     * 保存设备标签
     */
    @PostMapping("/save/{assetId}")
    @SaCheckPermission("equipment:asset:edit")
    @Operation(summary = "保存设备标签")
    public FrResult<?> save(@PathVariable String assetId,
                           @RequestBody List<String> tagIds) {
        return assetTagService.saveAssetTags(assetId, tagIds);
    }

    /**
     * 保存设备标签（带标签值）
     */
    @PostMapping("/saveWithValue/{assetId}")
    @SaCheckPermission("equipment:asset:edit")
    @Operation(summary = "保存设备标签（带标签值）")
    public FrResult<?> saveWithValue(@PathVariable String assetId,
                                     @RequestBody Map<String, String> tagData) {
        return assetTagService.saveAssetTagsWithValue(assetId, tagData);
    }

    /**
     * 批量保存设备标签
     */
    @PostMapping("/batchSave")
    @SaCheckPermission("equipment:asset:edit")
    @Operation(summary = "批量保存设备标签")
    public FrResult<?> batchSave(@RequestBody List<AssetTag> assetTags) {
        return assetTagService.batchSaveAssetTags(assetTags);
    }

    /**
     * 删除设备标签
     */
    @DeleteMapping("/del/{assetId}/{tagId}")
    @SaCheckPermission("equipment:asset:edit")
    @Operation(summary = "删除设备标签")
    public FrResult<?> delete(@PathVariable String assetId,
                             @PathVariable String tagId) {
        return assetTagService.deleteAssetTag(assetId, tagId);
    }

    /**
     * 删除设备的所有标签
     */
    @DeleteMapping("/delAll/{assetId}")
    @SaCheckPermission("equipment:asset:edit")
    @Operation(summary = "删除设备的所有标签")
    public FrResult<?> deleteAll(@PathVariable String assetId) {
        return assetTagService.deleteAssetTags(assetId);
    }

    /**
     * 复制设备标签
     */
    @PostMapping("/copy")
    @SaCheckPermission("equipment:asset:edit")
    @Operation(summary = "复制设备标签")
    public FrResult<?> copy(@RequestParam String fromAssetId,
                           @RequestParam String toAssetId) {
        return assetTagService.copyAssetTags(fromAssetId, toAssetId);
    }

    /**
     * 设置主要标签
     */
    @PostMapping("/setPrimary")
    @SaCheckPermission("equipment:asset:edit")
    @Operation(summary = "设置主要标签")
    public FrResult<?> setPrimary(@RequestParam String assetId,
                                  @RequestParam String tagId) {
        return assetTagService.setPrimaryTag(assetId, tagId);
    }

    /**
     * 查询设备的主要标签
     */
    @GetMapping("/primary/{assetId}")
    @SaCheckPermission("equipment:asset:query")
    @Operation(summary = "查询设备的主要标签")
    public FrResult<List<Map<String, Object>>> primary(@PathVariable String assetId) {
        List<Map<String, Object>> list = assetTagService.getPrimaryTags(assetId);
        return FrResult.success(list);
    }

    /**
     * 应用标签组合到设备
     */
    @PostMapping("/applyCombination")
    @SaCheckPermission("equipment:asset:edit")
    @Operation(summary = "应用标签组合到设备")
    public FrResult<?> applyCombination(@RequestParam String assetId,
                                        @RequestParam String combinationId) {
        return assetTagService.applyTagCombination(assetId, combinationId);
    }

    /**
     * 批量应用标签到设备
     */
    @PostMapping("/batchApply")
    @SaCheckPermission("equipment:asset:edit")
    @Operation(summary = "批量应用标签到设备")
    public FrResult<?> batchApply(@RequestParam List<String> assetIds,
                                  @RequestParam List<String> tagIds) {
        return assetTagService.batchApplyTags(assetIds, tagIds);
    }

    /**
     * 查询标签云数据
     */
    @GetMapping("/tagCloud")
    @SaCheckPermission("equipment:asset:query")
    @Operation(summary = "查询标签云数据")
    public FrResult<List<Map<String, Object>>> tagCloud(@RequestParam(defaultValue = "50") int limit) {
        List<Map<String, Object>> list = assetTagService.getTagCloud(limit);
        return FrResult.success(list);
    }

    /**
     * 查询设备标签统计
     */
    @GetMapping("/statistics")
    @SaCheckPermission("equipment:asset:query")
    @Operation(summary = "查询设备标签统计")
    public FrResult<Map<String, Object>> statistics() {
        Map<String, Object> statistics = assetTagService.getAssetTagStatistics();
        return FrResult.success(statistics);
    }

    /**
     * 查询相似设备（基于标签）
     */
    @GetMapping("/similar/{assetId}")
    @SaCheckPermission("equipment:asset:query")
    @Operation(summary = "查询相似设备（基于标签）")
    public FrResult<List<Map<String, Object>>> similar(@PathVariable String assetId,
                                                        @RequestParam(defaultValue = "10") int limit) {
        List<Map<String, Object>> list = assetTagService.getSimilarAssets(assetId, limit);
        return FrResult.success(list);
    }
}