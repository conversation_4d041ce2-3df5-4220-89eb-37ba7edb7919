package org.simple.equipment.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.simple.base.util.AuthUtil;
import org.simple.base.vo.FrResult;
import org.simple.equipment.entity.WorkOrder;
import org.simple.equipment.service.WorkOrderService;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 工单管理控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/equipment/work-order")
@RequiredArgsConstructor
@Tag(name = "工单管理", description = "工单管理相关接口")
public class WorkOrderController {

    private final WorkOrderService workOrderService;

    @Operation(summary = "获取工单列表", description = "获取工单列表")
    @GetMapping("/list")
    public FrResult<Page<WorkOrder>> list(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "工单编码") @RequestParam(required = false) String orderCode,
            @Parameter(description = "工单标题") @RequestParam(required = false) String orderTitle,
            @Parameter(description = "工单类型") @RequestParam(required = false) String orderType,
            @Parameter(description = "设备ID") @RequestParam(required = false) String equipmentId,
            @Parameter(description = "申请人ID") @RequestParam(required = false) String applicantId,
            @Parameter(description = "处理人ID") @RequestParam(required = false) String assignedUserId,
            @Parameter(description = "工单状态") @RequestParam(required = false) String orderStatus,
            @Parameter(description = "优先级") @RequestParam(required = false) String priority) {

        QueryWrapper<WorkOrder> queryWrapper = new QueryWrapper<>();
        //queryWrapper.eq("tenant_id", AuthUtil.getTenantId());
        
        if (orderCode != null && !orderCode.trim().isEmpty()) {
            queryWrapper.like("order_code", orderCode);
        }
        if (orderTitle != null && !orderTitle.trim().isEmpty()) {
            queryWrapper.like("order_title", orderTitle);
        }
        if (orderType != null && !orderType.trim().isEmpty()) {
            queryWrapper.eq("order_type", orderType);
        }
        if (equipmentId != null && !equipmentId.trim().isEmpty()) {
            queryWrapper.eq("equipment_id", equipmentId);
        }
        if (applicantId != null && !applicantId.trim().isEmpty()) {
            queryWrapper.eq("applicant_id", applicantId);
        }
        if (assignedUserId != null && !assignedUserId.trim().isEmpty()) {
            queryWrapper.eq("assigned_user_id", assignedUserId);
        }
        if (orderStatus != null && !orderStatus.trim().isEmpty()) {
            queryWrapper.eq("order_status", orderStatus);
        }
        if (priority != null && !priority.trim().isEmpty()) {
            queryWrapper.eq("priority", priority);
        }
        
        queryWrapper.orderByDesc("apply_time");
        
        Page<WorkOrder> pageResult = workOrderService.page(new Page<>(page, size), queryWrapper);
        return FrResult.success(pageResult);
    }

    @Operation(summary = "获取工单详情", description = "根据ID获取工单详情")
    @GetMapping("/{id}")
    public FrResult<WorkOrder> getById(@PathVariable String id) {
        WorkOrder workOrder = workOrderService.getById(id);
        if (workOrder == null) {
            return FrResult.failed("工单不存在");
        }
        return FrResult.success(workOrder);
    }

    @Operation(summary = "创建工单", description = "创建新的工单")
    @PostMapping
    public FrResult<Boolean> create(@Valid @RequestBody WorkOrder workOrder) {
        boolean success = workOrderService.createWorkOrder(workOrder);
        return FrResult.success(success);
    }

    @Operation(summary = "更新工单", description = "更新工单信息")
    @PutMapping
    public FrResult<Boolean> update(@Valid @RequestBody WorkOrder workOrder) {
        boolean success = workOrderService.updateWorkOrder(workOrder);
        return FrResult.success(success);
    }

    @Operation(summary = "删除工单", description = "删除指定的工单")
    @DeleteMapping("/{id}")
    public FrResult<Boolean> delete(@PathVariable String id) {
        boolean success = workOrderService.deleteWorkOrder(id);
        return FrResult.success(success);
    }

    @Operation(summary = "分配工单", description = "将工单分配给指定用户")
    @PutMapping("/{id}/assign")
    public FrResult<Boolean> assign(
            @PathVariable String id,
            @RequestParam String userId,
            @RequestParam String userName) {
        boolean success = workOrderService.assignWorkOrder(id, userId, userName);
        return FrResult.success(success);
    }

    @Operation(summary = "开始处理工单", description = "开始处理工单")
    @PutMapping("/{id}/start")
    public FrResult<Boolean> start(@PathVariable String id) {
        boolean success = workOrderService.startWorkOrder(id);
        return FrResult.success(success);
    }

    @Operation(summary = "完成工单", description = "完成工单")
    @PutMapping("/{id}/complete")
    public FrResult<Boolean> complete(
            @PathVariable String id,
            @RequestParam String solution) {
        boolean success = workOrderService.completeWorkOrder(id, solution);
        return FrResult.success(success);
    }

    @Operation(summary = "取消工单", description = "取消工单")
    @PutMapping("/{id}/cancel")
    public FrResult<Boolean> cancel(
            @PathVariable String id,
            @RequestParam String reason) {
        boolean success = workOrderService.cancelWorkOrder(id, reason);
        return FrResult.success(success);
    }

    @Operation(summary = "获取工单统计", description = "获取工单统计信息")
    @GetMapping("/statistics")
    public FrResult<Map<String, Object>> getStatistics() {
        Map<String, Object> statistics = workOrderService.getWorkOrderStatistics();
        return FrResult.success(statistics);
    }

    @Operation(summary = "获取用户工单", description = "获取指定用户的工单列表")
    @GetMapping("/user/{userId}")
    public FrResult<List<WorkOrder>> getUserWorkOrders(@PathVariable String userId) {
        List<WorkOrder> workOrders = workOrderService.getUserWorkOrders(userId);
        return FrResult.success(workOrders);
    }

    @Operation(summary = "获取设备工单", description = "获取指定设备的工单列表")
    @GetMapping("/equipment/{equipmentId}")
    public FrResult<List<WorkOrder>> getEquipmentWorkOrders(@PathVariable String equipmentId) {
        List<WorkOrder> workOrders = workOrderService.getEquipmentWorkOrders(equipmentId);
        return FrResult.success(workOrders);
    }

    @Operation(summary = "获取待处理工单", description = "获取待处理工单列表")
    @GetMapping("/pending")
    public FrResult<List<WorkOrder>> getPendingWorkOrders() {
        List<WorkOrder> workOrders = workOrderService.getPendingWorkOrders();
        return FrResult.success(workOrders);
    }

    @Operation(summary = "获取超时工单", description = "获取超时工单列表")
    @GetMapping("/timeout")
    public FrResult<List<WorkOrder>> getTimeoutWorkOrders(
            @Parameter(description = "超时小时数") @RequestParam(defaultValue = "24") Integer hours) {
        List<WorkOrder> workOrders = workOrderService.getTimeoutWorkOrders(hours);
        return FrResult.success(workOrders);
    }

    @Operation(summary = "检查工单编码是否存在", description = "检查工单编码是否已存在")
    @GetMapping("/check-code")
    public FrResult<Boolean> checkOrderCode(
            @RequestParam String orderCode,
            @RequestParam(required = false) String excludeId) {
        boolean exists = workOrderService.checkOrderCodeExists(orderCode, excludeId);
        return FrResult.success(exists);
    }

    @Operation(summary = "生成工单编码", description = "生成工单编码")
    @GetMapping("/generate-code")
    public FrResult<String> generateOrderCode(@RequestParam String orderType) {
        String orderCode = workOrderService.generateOrderCode(orderType);
        return FrResult.success(orderCode);
    }

    @Operation(summary = "自动分配工单", description = "自动分配工单")
    @PostMapping("/{id}/auto-assign")
    public FrResult<Boolean> autoAssign(@PathVariable String id) {
        boolean success = workOrderService.autoAssignWorkOrder(id);
        return FrResult.success(success);
    }

    @Operation(summary = "获取处理时长统计", description = "获取工单处理时长统计")
    @GetMapping("/processing-time-stats")
    public FrResult<List<Map<String, Object>>> getProcessingTimeStatistics() {
        List<Map<String, Object>> statistics = workOrderService.getProcessingTimeStatistics();
        return FrResult.success(statistics);
    }

    @Operation(summary = "催办工单", description = "催办工单")
    @PostMapping("/{id}/urge")
    public FrResult<Boolean> urge(@PathVariable String id) {
        boolean success = workOrderService.urgeWorkOrder(id);
        return FrResult.success(success);
    }
}