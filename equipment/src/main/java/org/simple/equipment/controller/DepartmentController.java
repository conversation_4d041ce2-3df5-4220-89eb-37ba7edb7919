package org.simple.equipment.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.simple.base.vo.FrResult;
import org.simple.equipment.entity.Department;
import org.simple.equipment.service.DepartmentService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 科室管理控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/equipment/department")
@Tag(name = "科室管理", description = "科室管理")
public class DepartmentController {

    @Resource
    private DepartmentService departmentService;

    /**
     * 查询科室树形结构
     */
    @GetMapping("/tree")
    @SaCheckPermission("equipment:department:query")
    @Operation(summary = "查询科室树形结构")
    public FrResult<List<Tree<String>>> tree() {
        List<Tree<String>> tree = departmentService.getDepartmentTree();
        return FrResult.success(tree);
    }

    /**
     * 查询科室列表
     */
    @GetMapping("/list")
    @SaCheckPermission("equipment:department:query")
    @Operation(summary = "查询科室列表")
    public FrResult<List<Department>> list(Department department) {
        List<Department> list = departmentService.getDepartmentList(department);
        return FrResult.success(list);
    }

    /**
     * 查询科室详情
     */
    @GetMapping("/detail/{id}")
    @SaCheckPermission("equipment:department:query")
    @Operation(summary = "查询科室详情")
    public FrResult<Department> detail(@PathVariable String id) {
        Department department = departmentService.getById(id);
        return department != null ? FrResult.success(department) : FrResult.failed("科室不存在");
    }

    /**
     * 新增科室
     */
    @PostMapping("/add")
    @SaCheckPermission("equipment:department:add")
    @Operation(summary = "新增科室")
    public FrResult<?> add(@RequestBody @Validated Department department) {
        return departmentService.saveDepartment(department);
    }

    /**
     * 修改科室
     */
    @PostMapping("/edit")
    @SaCheckPermission("equipment:department:edit")
    @Operation(summary = "修改科室")
    public FrResult<?> edit(@RequestBody @Validated Department department) {
        if (StrUtil.isBlank(department.getId())) {
            return FrResult.failed("科室ID不能为空");
        }
        return departmentService.updateDepartment(department);
    }

    /**
     * 删除科室
     */
    @DeleteMapping("/del/{id}")
    @SaCheckPermission("equipment:department:del")
    @Operation(summary = "删除科室")
    public FrResult<?> delete(@PathVariable String id) {
        return departmentService.deleteDepartment(id);
    }

    /**
     * 检查科室编码是否存在
     */
    @GetMapping("/checkDeptCode")
    @SaCheckPermission("equipment:department:query")
    @Operation(summary = "检查科室编码是否存在")
    public FrResult<Boolean> checkDeptCode(@RequestParam String deptCode,
                                         @RequestParam(required = false) String excludeId) {
        boolean exists = departmentService.checkDeptCodeExists(deptCode, excludeId);
        return FrResult.success(exists);
    }

    /**
     * 查询科室选项（用于下拉选择）
     */
    @GetMapping("/options")
    @SaCheckPermission("equipment:department:query")
    @Operation(summary = "查询科室选项")
    public FrResult<List<Tree<String>>> options() {
        List<Tree<String>> tree = departmentService.getDepartmentTree();
        return FrResult.success(tree);
    }
}