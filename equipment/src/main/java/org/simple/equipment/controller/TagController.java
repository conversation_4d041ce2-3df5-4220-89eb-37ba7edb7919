package org.simple.equipment.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.simple.base.vo.FrResult;
import org.simple.equipment.service.TagService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 设备标签控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/equipment/tag")
@Tag(name = "设备标签管理", description = "设备标签管理")
public class TagController {

    @Resource
    private TagService tagService;

    /**
     * 查询标签列表
     */
    @GetMapping("/list")
    @SaCheckPermission("equipment:tag:query")
    @Operation(summary = "查询标签列表")
    public FrResult<List<org.simple.equipment.entity.Tag>> list(org.simple.equipment.entity.Tag tag) {
        List<org.simple.equipment.entity.Tag> list = tagService.getTagList(tag);
        return FrResult.success(list);
    }

    /**
     * 查询有效的标签列表
     */
    @GetMapping("/activeList")
    @SaCheckPermission("equipment:tag:query")
    @Operation(summary = "查询有效的标签列表")
    public FrResult<List<org.simple.equipment.entity.Tag>> activeList() {
        List<org.simple.equipment.entity.Tag> list = tagService.getActiveTagList();
        return FrResult.success(list);
    }

    /**
     * 根据分类查询标签
     */
    @GetMapping("/listByCategory")
    @SaCheckPermission("equipment:tag:query")
    @Operation(summary = "根据分类查询标签")
    public FrResult<List<org.simple.equipment.entity.Tag>> listByCategory(@RequestParam String categoryId) {
        List<org.simple.equipment.entity.Tag> list = tagService.getTagsByCategory(categoryId);
        return FrResult.success(list);
    }

    /**
     * 根据标签类型查询标签
     */
    @GetMapping("/listByType")
    @SaCheckPermission("equipment:tag:query")
    @Operation(summary = "根据标签类型查询标签")
    public FrResult<List<org.simple.equipment.entity.Tag>> listByType(@RequestParam String tagType) {
        List<org.simple.equipment.entity.Tag> list = tagService.getTagsByType(tagType);
        return FrResult.success(list);
    }

    /**
     * 查询标签详情
     */
    @GetMapping("/detail/{id}")
    @SaCheckPermission("equipment:tag:query")
    @Operation(summary = "查询标签详情")
    public FrResult<org.simple.equipment.entity.Tag> detail(@PathVariable String id) {
        org.simple.equipment.entity.Tag tag = tagService.getById(id);
        return tag != null ? FrResult.success(tag) : FrResult.failed("标签不存在");
    }

    /**
     * 新增标签
     */
    @PostMapping("/add")
    @SaCheckPermission("equipment:tag:add")
    @Operation(summary = "新增标签")
    public FrResult<?> add(@RequestBody @Validated org.simple.equipment.entity.Tag tag) {
        return tagService.saveTag(tag);
    }

    /**
     * 修改标签
     */
    @PostMapping("/edit")
    @SaCheckPermission("equipment:tag:edit")
    @Operation(summary = "修改标签")
    public FrResult<?> edit(@RequestBody @Validated org.simple.equipment.entity.Tag tag) {
        if (StrUtil.isBlank(tag.getId())) {
            return FrResult.failed("标签ID不能为空");
        }
        return tagService.updateTag(tag);
    }

    /**
     * 删除标签
     */
    @DeleteMapping("/del/{id}")
    @SaCheckPermission("equipment:tag:del")
    @Operation(summary = "删除标签")
    public FrResult<?> delete(@PathVariable String id) {
        return tagService.deleteTag(id);
    }

    /**
     * 检查标签编码是否存在
     */
    @GetMapping("/checkTagCode")
    @SaCheckPermission("equipment:tag:query")
    @Operation(summary = "检查标签编码是否存在")
    public FrResult<Boolean> checkTagCode(@RequestParam String tagCode,
                                          @RequestParam(required = false) String excludeId) {
        boolean exists = tagService.checkTagCodeExists(tagCode, excludeId);
        return FrResult.success(exists);
    }

    /**
     * 更新标签状态
     */
    @PostMapping("/updateStatus")
    @SaCheckPermission("equipment:tag:edit")
    @Operation(summary = "更新标签状态")
    public FrResult<?> updateStatus(@RequestParam String id,
                                    @RequestParam String isEnabled) {
        return tagService.updateTagStatus(id, isEnabled);
    }

    /**
     * 查询热门标签
     */
    @GetMapping("/popular")
    @SaCheckPermission("equipment:tag:query")
    @Operation(summary = "查询热门标签")
    public FrResult<List<org.simple.equipment.entity.Tag>> popular(@RequestParam(defaultValue = "10") int limit) {
        List<org.simple.equipment.entity.Tag> list = tagService.getPopularTags(limit);
        return FrResult.success(list);
    }

    /**
     * 搜索标签
     */
    @GetMapping("/search")
    @SaCheckPermission("equipment:tag:query")
    @Operation(summary = "搜索标签")
    public FrResult<List<org.simple.equipment.entity.Tag>> search(@RequestParam String keyword,
                                                                  @RequestParam(defaultValue = "10") int limit) {
        List<org.simple.equipment.entity.Tag> list = tagService.searchTags(keyword, limit);
        return FrResult.success(list);
    }

    /**
     * 查询标签使用统计
     */
    @GetMapping("/usageStatistics")
    @SaCheckPermission("equipment:tag:query")
    @Operation(summary = "查询标签使用统计")
    public FrResult<List<Map<String, Object>>> usageStatistics() {
        List<Map<String, Object>> statistics = tagService.getUsageStatistics();
        return FrResult.success(statistics);
    }

    /**
     * 批量创建标签
     */
    @PostMapping("/batchCreate")
    @SaCheckPermission("equipment:tag:add")
    @Operation(summary = "批量创建标签")
    public FrResult<?> batchCreate(@RequestParam List<String> tagNames,
                                   @RequestParam(required = false) String categoryId) {
        return tagService.batchCreateTags(tagNames, categoryId);
    }

    /**
     * 合并标签
     */
    @PostMapping("/merge")
    @SaCheckPermission("equipment:tag:edit")
    @Operation(summary = "合并标签")
    public FrResult<?> merge(@RequestParam String sourceTagId,
                            @RequestParam String targetTagId) {
        return tagService.mergeTags(sourceTagId, targetTagId);
    }

    /**
     * 导入标签
     */
    @PostMapping("/import")
    @SaCheckPermission("equipment:tag:add")
    @Operation(summary = "导入标签")
    public FrResult<?> importTags(@RequestBody String tagData) {
        return tagService.importTags(tagData);
    }

    /**
     * 导出标签
     */
    @GetMapping("/export")
    @SaCheckPermission("equipment:tag:query")
    @Operation(summary = "导出标签")
    public FrResult<String> export(@RequestParam(required = false) String categoryId) {
        return tagService.exportTags(categoryId);
    }

    /**
     * 获取标签类型选项
     */
    @GetMapping("/tagTypes")
    @SaCheckPermission("equipment:tag:query")
    @Operation(summary = "获取标签类型选项")
    public FrResult<List<Object>> tagTypes() {
        List<Object> tagTypes = List.of(
            new Object[]{"system", "系统标签"},
            new Object[]{"custom", "自定义标签"},
            new Object[]{"business", "业务标签"},
            new Object[]{"status", "状态标签"},
            new Object[]{"property", "属性标签"}
        );
        return FrResult.success(tagTypes);
    }
}