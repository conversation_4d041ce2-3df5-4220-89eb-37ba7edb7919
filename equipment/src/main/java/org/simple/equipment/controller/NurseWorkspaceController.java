package org.simple.equipment.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.simple.base.vo.FrResult;
import org.simple.equipment.dto.NurseWorkspaceData;
import org.simple.equipment.dto.QuickActionConfig;
import org.simple.equipment.entity.NurseQuickAction;
import org.simple.equipment.entity.NurseTaskOverview;
import org.simple.equipment.entity.NurseWorkspaceConfig;
import org.simple.equipment.service.NurseWorkspaceService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 护士工作台控制器
 * 
 * <AUTHOR>
 */
@Tag(name = "护士工作台", description = "护士专用移动端工作台相关接口")
@RestController
@RequestMapping("/api/equipment/nurse-workspace")
@RequiredArgsConstructor
@Validated
public class NurseWorkspaceController {

    private final NurseWorkspaceService nurseWorkspaceService;

    @Operation(summary = "获取护士工作台数据", description = "获取护士工作台的完整数据，包括任务概览、快速操作等")
    @GetMapping("/dashboard/{userId}")
    public FrResult<NurseWorkspaceData> getNurseWorkspaceData(@PathVariable String userId) {
        NurseWorkspaceData workspaceData = nurseWorkspaceService.getNurseWorkspaceData(userId);
        return FrResult.success(workspaceData);
    }

    @Operation(summary = "获取今日任务概览", description = "获取护士今日的任务概览统计信息")
    @GetMapping("/today-overview/{userId}")
    public FrResult<NurseTaskOverview> getTodayTaskOverview(@PathVariable String userId) {
        NurseTaskOverview overview = nurseWorkspaceService.getTodayTaskOverview(userId);
        return FrResult.success(overview);
    }

    @Operation(summary = "更新任务概览统计", description = "更新指定日期的任务概览统计数据")
    @PostMapping("/update-overview/{userId}")
    public FrResult<Boolean> updateTaskOverviewStatistics(
            @PathVariable String userId,
            @RequestParam(required = false) LocalDate taskDate) {
        
        if (taskDate == null) {
            taskDate = LocalDate.now();
        }
        
        Boolean result = nurseWorkspaceService.updateTaskOverviewStatistics(userId, taskDate);
        return FrResult.success(result);
    }

    @Operation(summary = "获取快速操作配置", description = "获取护士的个性化快速操作配置")
    @GetMapping("/quick-actions/{userId}")
    public FrResult<List<NurseQuickAction>> getQuickActions(@PathVariable String userId) {
        List<NurseQuickAction> actions = nurseWorkspaceService.getQuickActions(userId);
        return FrResult.success(actions);
    }

    @Operation(summary = "配置快速操作", description = "配置护士的个性化快速操作")
    @PostMapping("/quick-actions/{userId}")
    public FrResult<Boolean> configureQuickActions(
            @PathVariable String userId,
            @Valid @RequestBody List<QuickActionConfig> actions) {
        
        Boolean result = nurseWorkspaceService.configureQuickActions(userId, actions);
        return FrResult.success(result);
    }

    @Operation(summary = "执行快速操作", description = "执行指定的快速操作")
    @PostMapping("/execute-action/{userId}")
    public FrResult<Map<String, Object>> executeQuickAction(
            @PathVariable String userId,
            @RequestParam String actionCode) {
        
        Map<String, Object> result = nurseWorkspaceService.executeQuickAction(userId, actionCode);
        return FrResult.success(result);
    }

    @Operation(summary = "获取工作台配置", description = "获取护士的工作台个性化配置")
    @GetMapping("/config/{userId}")
    public FrResult<NurseWorkspaceConfig> getWorkspaceConfig(@PathVariable String userId) {
        NurseWorkspaceConfig config = nurseWorkspaceService.getWorkspaceConfig(userId);
        return FrResult.success(config);
    }

    @Operation(summary = "更新工作台配置", description = "更新护士的工作台个性化配置")
    @PutMapping("/config/{userId}")
    public FrResult<Boolean> updateWorkspaceConfig(
            @PathVariable String userId,
            @RequestBody Map<String, Object> config) {
        
        Boolean result = nurseWorkspaceService.updateWorkspaceConfig(userId, config);
        return FrResult.success(result);
    }

    @Operation(summary = "获取个人统计", description = "获取护士的个人工作统计数据")
    @GetMapping("/statistics/{userId}")
    public FrResult<Map<String, Object>> getPersonalStatistics(
            @PathVariable String userId,
            @RequestParam(defaultValue = "DAILY") String period) {
        
        Map<String, Object> statistics = nurseWorkspaceService.getPersonalStatistics(userId, period);
        return FrResult.success(statistics);
    }

    @Operation(summary = "获取科室排行榜", description = "获取科室内的护士排行榜数据")
    @GetMapping("/ranking")
    public FrResult<List<Map<String, Object>>> getDepartmentRanking(
            @RequestParam String departmentId,
            @RequestParam(defaultValue = "EFFICIENCY") String category,
            @RequestParam(defaultValue = "DAILY") String period) {
        
        List<Map<String, Object>> rankings = nurseWorkspaceService.getDepartmentRanking(departmentId, category, period);
        return FrResult.success(rankings);
    }

    @Operation(summary = "初始化护士工作台", description = "为新护士初始化工作台配置")
    @PostMapping("/initialize/{userId}")
    public FrResult<Boolean> initializeNurseWorkspace(
            @PathVariable String userId,
            @RequestParam String departmentId) {
        
        Boolean result = nurseWorkspaceService.initializeNurseWorkspace(userId, departmentId);
        return FrResult.success(result);
    }

    @Operation(summary = "获取工作负荷分析", description = "获取护士的工作负荷分析报告")
    @GetMapping("/workload-analysis/{userId}")
    public FrResult<Map<String, Object>> getWorkloadAnalysis(
            @PathVariable String userId,
            @RequestParam(defaultValue = "7") Integer days) {
        
        Map<String, Object> analysis = nurseWorkspaceService.getWorkloadAnalysis(userId, days);
        return FrResult.success(analysis);
    }

    @Operation(summary = "语音操作记录", description = "记录护士的语音操作日志")
    @PostMapping("/voice-operation/{userId}")
    public FrResult<Void> recordVoiceOperation(
            @PathVariable String userId,
            @RequestParam String voiceCommand,
            @RequestParam String result) {
        
        nurseWorkspaceService.recordVoiceOperation(userId, voiceCommand, result);
        return FrResult.success();
    }

    @Operation(summary = "护士夜班快速启动", description = "护士夜班专用的快速启动接口")
    @PostMapping("/night-shift-start/{userId}")
    public FrResult<NurseWorkspaceData> nightShiftQuickStart(@PathVariable String userId) {
        // 获取完整工作台数据
        NurseWorkspaceData workspaceData = nurseWorkspaceService.getNurseWorkspaceData(userId);
        
        // 自动更新今日概览
        nurseWorkspaceService.updateTaskOverviewStatistics(userId, LocalDate.now());
        
        return FrResult.success(workspaceData);
    }

    @Operation(summary = "快速扫码点检", description = "护士快速扫码点检的简化接口")
    @PostMapping("/quick-scan/{userId}")
    public FrResult<Map<String, Object>> quickScanInspection(
            @PathVariable String userId,
            @RequestParam String qrCodeData) {
        
        // 执行扫码快速操作
        Map<String, Object> result = nurseWorkspaceService.executeQuickAction(userId, "SCAN_EQUIPMENT");
        result.put("qrCodeData", qrCodeData);
        result.put("autoStart", true);
        
        return FrResult.success(result);
    }

    @Operation(summary = "快速异常报告", description = "护士快速异常报告的简化接口")
    @PostMapping("/quick-exception-report/{userId}")
    public FrResult<Map<String, Object>> quickExceptionReport(
            @PathVariable String userId,
            @RequestParam String equipmentId,
            @RequestParam String description,
            @RequestParam(required = false) String voiceNote) {
        
        // 执行快速异常报告
        Map<String, Object> result = nurseWorkspaceService.executeQuickAction(userId, "REPORT_EXCEPTION");
        result.put("equipmentId", equipmentId);
        result.put("description", description);
        result.put("voiceNote", voiceNote);
        result.put("autoSubmit", true);
        
        // 记录语音操作
        if (voiceNote != null) {
            nurseWorkspaceService.recordVoiceOperation(userId, "异常报告", "成功");
        }
        
        return FrResult.success(result);
    }
}