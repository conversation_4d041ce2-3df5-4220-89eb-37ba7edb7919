package org.simple.equipment.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.simple.base.dto.mybatis.Page;
import org.simple.base.vo.FrResult;
import org.simple.equipment.dto.AssetDto;
import org.simple.equipment.entity.Asset;
import org.simple.equipment.service.AssetService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 设备档案控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/equipment/asset")
@Tag(name = "设备档案管理", description = "设备档案管理")
public class AssetController {

    @Resource
    private AssetService assetService;

    /**
     * 分页查询设备档案
     */
    @GetMapping("/list")
    @SaCheckPermission("equipment:asset:query")
    @Operation(summary = "分页查询设备档案")
    public FrResult<IPage<AssetDto>> list(Page<AssetDto> page, Asset asset) {
        IPage<AssetDto> result = assetService.listAssets(page, asset);
        return FrResult.success(result);
    }

    /**
     * 查询设备档案详情
     */
    @GetMapping("/detail/{id}")
    @SaCheckPermission("equipment:asset:query")
    @Operation(summary = "查询设备档案详情")
    public FrResult<AssetDto> detail(@PathVariable String id) {
        AssetDto asset = assetService.getAssetById(id);
        return asset != null ? FrResult.success(asset) : FrResult.failed("设备不存在");
    }

    /**
     * 新增设备档案
     */
    @PostMapping("/add")
    @SaCheckPermission("equipment:asset:add")
    @Operation(summary = "新增设备档案")
    public FrResult<?> add(@RequestBody @Validated Asset asset) {
        return assetService.saveAsset(asset);
    }

    /**
     * 修改设备档案
     */
    @PostMapping("/edit")
    @SaCheckPermission("equipment:asset:edit")
    @Operation(summary = "修改设备档案")
    public FrResult<?> edit(@RequestBody @Validated Asset asset) {
        if (StrUtil.isBlank(asset.getId())) {
            return FrResult.failed("设备ID不能为空");
        }
        return assetService.updateAsset(asset);
    }

    /**
     * 删除设备档案
     */
    @DeleteMapping("/del/{id}")
    @SaCheckPermission("equipment:asset:del")
    @Operation(summary = "删除设备档案")
    public FrResult<?> delete(@PathVariable String id) {
        return assetService.deleteAsset(id);
    }

    /**
     * 批量删除设备档案
     */
    @DeleteMapping("/batchDel")
    @SaCheckPermission("equipment:asset:del")
    @Operation(summary = "批量删除设备档案")
    public FrResult<?> batchDelete(@RequestParam String ids) {
        if (StrUtil.isBlank(ids)) {
            return FrResult.failed("请选择要删除的设备");
        }
        List<String> idList = Arrays.asList(ids.split(","));
        return assetService.batchDeleteAssets(idList);
    }

    /**
     * 生成设备编码
     */
    @GetMapping("/generateCode")
    @SaCheckPermission("equipment:asset:add")
    @Operation(summary = "生成设备编码")
    public FrResult<String> generateCode(@RequestParam(required = false) String categoryId) {
        String code = assetService.generateEquipmentCode(categoryId);
        return FrResult.success(code);
    }

    /**
     * 批量导入设备档案
     */
    @PostMapping("/import")
    @SaCheckPermission("equipment:asset:import")
    @Operation(summary = "批量导入设备档案")
    public FrResult<?> importAssets(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return FrResult.failed("请选择要导入的文件");
        }
        
        String fileName = file.getOriginalFilename();
        if (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls")) {
            return FrResult.failed("文件格式不正确，请上传Excel文件");
        }
        
        return assetService.importAssets(file);
    }

    /**
     * 导出设备档案
     */
    @GetMapping("/export")
    @SaCheckPermission("equipment:asset:export")
    @Operation(summary = "导出设备档案")
    public void export(Asset asset, HttpServletResponse response) throws IOException {
        try {
            byte[] data = assetService.exportAssets(asset);
            
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            
            String fileName = "设备档案_" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + ".xlsx";
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            
            response.getOutputStream().write(data);
            response.getOutputStream().flush();
        } catch (Exception e) {
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().write("{\"code\":1,\"msg\":\"导出失败：" + e.getMessage() + "\"}");
        }
    }

    /**
     * 查询科室下的设备列表
     */
    @GetMapping("/listByDepartment")
    @SaCheckPermission("equipment:asset:query")
    @Operation(summary = "查询科室下的设备列表")
    public FrResult<List<AssetDto>> listByDepartment(@RequestParam String departmentId) {
        List<AssetDto> assets = assetService.getAssetsByDepartment(departmentId);
        return FrResult.success(assets);
    }

    /**
     * 更新设备状态
     */
    @PostMapping("/updateStatus")
    @SaCheckPermission("equipment:asset:edit")
    @Operation(summary = "更新设备状态")
    public FrResult<?> updateStatus(@RequestParam String assetId, @RequestParam String status) {
        return assetService.updateAssetStatus(assetId, status);
    }

    /**
     * 设备状态统计
     */
    @GetMapping("/statusStatistics")
    @SaCheckPermission("equipment:asset:query")
    @Operation(summary = "设备状态统计")
    public FrResult<List<Map<String, Object>>> statusStatistics() {
        List<Map<String, Object>> statistics = assetService.getStatusStatistics();
        return FrResult.success(statistics);
    }

    /**
     * 设备调拨
     */
    @PostMapping("/transfer")
    @SaCheckPermission("equipment:asset:transfer")
    @Operation(summary = "设备调拨")
    public FrResult<?> transfer(@RequestParam String assetId,
                              @RequestParam String newDepartmentId,
                              @RequestParam(required = false) String newLocationId,
                              @RequestParam(required = false) String reason) {
        return assetService.transferAsset(assetId, newDepartmentId, newLocationId, reason);
    }

    /**
     * 打印设备档案卡片
     */
    @GetMapping("/print/{id}")
    @SaCheckPermission("equipment:asset:print")
    @Operation(summary = "打印设备档案卡片")
    public void printAssetCard(@PathVariable String id, HttpServletResponse response) throws IOException {
        try {
            AssetDto asset = assetService.getAssetById(id);
            if (asset == null) {
                response.setContentType("application/json");
                response.setCharacterEncoding("utf-8");
                response.getWriter().write("{\"code\":1,\"msg\":\"设备不存在\"}");
                return;
            }

            // TODO: 生成打印模板（PDF或HTML）
            // 这里可以集成JasperReports或iText等PDF生成工具
            
            response.setContentType("text/html");
            response.setCharacterEncoding("utf-8");
            
            StringBuilder html = new StringBuilder();
            html.append("<html><head><title>设备档案卡片</title></head><body>");
            html.append("<h1>设备档案卡片</h1>");
            html.append("<table border='1' style='border-collapse: collapse;'>");
            html.append("<tr><td>设备编码</td><td>").append(asset.getEquipmentCode()).append("</td></tr>");
            html.append("<tr><td>设备名称</td><td>").append(asset.getEquipmentName()).append("</td></tr>");
            html.append("<tr><td>设备分类</td><td>").append(asset.getCategoryName()).append("</td></tr>");
            html.append("<tr><td>设备类型</td><td>").append(asset.getTypeName()).append("</td></tr>");
            html.append("<tr><td>型号</td><td>").append(asset.getModel()).append("</td></tr>");
            html.append("<tr><td>生产商</td><td>").append(asset.getManufacturer()).append("</td></tr>");
            html.append("<tr><td>所属科室</td><td>").append(asset.getDepartmentName()).append("</td></tr>");
            html.append("<tr><td>所在位置</td><td>").append(asset.getLocationName()).append("</td></tr>");
            html.append("<tr><td>设备状态</td><td>").append(asset.getEquipmentStatusName()).append("</td></tr>");
            html.append("</table>");
            html.append("</body></html>");
            
            response.getWriter().write(html.toString());
        } catch (Exception e) {
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().write("{\"code\":1,\"msg\":\"打印失败：" + e.getMessage() + "\"}");
        }
    }
}