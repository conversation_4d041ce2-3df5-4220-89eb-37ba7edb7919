package org.simple.equipment.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.simple.base.vo.FrResult;
import org.simple.base.util.SecurityUtils;
import org.simple.equipment.entity.InspectionApproval;
import org.simple.equipment.service.InspectionApprovalService;



import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 设备点检审核控制器
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/equipment/inspection/approval")
@Tag(name = "设备点检审核", description = "设备点检审核相关接口")
public class InspectionApprovalController {

    @Autowired
    private InspectionApprovalService approvalService;

    @GetMapping("/page")
    @Operation(summary = "分页查询审核记录")
    public FrResult<IPage<InspectionApproval>> pageApprovals(
            @Parameter(description = "当前页") @RequestParam(defaultValue = "1") int current,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "审核状态") @RequestParam(required = false) String approvalStatus,
            @Parameter(description = "质量等级") @RequestParam(required = false) String qualityLevel,
            @Parameter(description = "设备名称") @RequestParam(required = false) String assetName,
            @Parameter(description = "执行人员") @RequestParam(required = false) String executorName,
            @Parameter(description = "审核人员") @RequestParam(required = false) String approverName,
            @Parameter(description = "开始时间") @RequestParam(required = false) LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) LocalDateTime endTime,
            @Parameter(description = "是否需要后续处理") @RequestParam(required = false) Boolean needFollowUp) {
        
        Map<String, Object> params = Map.of(
            "approvalStatus", approvalStatus != null ? approvalStatus : "",
            "qualityLevel", qualityLevel != null ? qualityLevel : "",
            "assetName", assetName != null ? assetName : "",
            "executorName", executorName != null ? executorName : "",
            "approverName", approverName != null ? approverName : "",
            "startTime", startTime,
            "endTime", endTime,
            "needFollowUp", needFollowUp
        );
        
        IPage<InspectionApproval> page = approvalService.pageApprovals(current, size, params);
        return FrResult.success(page);
    }

    @PostMapping("/submit/{inspectionTaskId}")
    @Operation(summary = "提交审核")
    public FrResult<Void> submitApproval(
            @Parameter(description = "点检任务ID") @PathVariable @NotBlank String inspectionTaskId) {
        
        String userId = SecurityUtils.getLoginUserId();
        boolean success = approvalService.submitApproval(inspectionTaskId, userId);
        
        return success ? FrResult.success() : FrResult.failed("提交审核失败");
    }

    @PostMapping("/approve/pass")
    @Operation(summary = "审核通过")
    public FrResult<Void> approvePass(@Valid @RequestBody ApprovalRequest request) {
        String approverId = SecurityUtils.getLoginUserId();
        boolean success = approvalService.approvePass(request.getApprovalId(), approverId, request.getComment());
        
        return success ? FrResult.success() : FrResult.failed("审核通过失败");
    }

    @PostMapping("/approve/reject")
    @Operation(summary = "审核驳回")
    public FrResult<Void> approveReject(@Valid @RequestBody ApprovalRequest request) {
        String approverId = SecurityUtils.getLoginUserId();
        boolean success = approvalService.approveReject(
            request.getApprovalId(), approverId, request.getComment(), request.getReason());
        
        return success ? FrResult.success() : FrResult.failed("审核驳回失败");
    }

    @PostMapping("/approve/return")
    @Operation(summary = "审核退回")
    public FrResult<Void> approveReturn(@Valid @RequestBody ApprovalRequest request) {
        String approverId = SecurityUtils.getLoginUserId();
        boolean success = approvalService.approveReturn(
            request.getApprovalId(), approverId, request.getComment(), request.getReason());
        
        return success ? FrResult.success() : FrResult.failed("审核退回失败");
    }

    @PostMapping("/withdraw/{approvalId}")
    @Operation(summary = "撤回审核")
    public FrResult<Void> withdrawApproval(
            @Parameter(description = "审核ID") @PathVariable @NotBlank String approvalId) {
        
        String userId = SecurityUtils.getLoginUserId();
        boolean success = approvalService.withdrawApproval(approvalId, userId);
        
        return success ? FrResult.success() : FrResult.failed("撤回审核失败");
    }

    @PostMapping("/batch/approve")
    @Operation(summary = "批量审核")
    public FrResult<Void> batchApprove(@Valid @RequestBody BatchApprovalRequest request) {
        String approverId = SecurityUtils.getLoginUserId();
        boolean success = approvalService.batchApprove(
            request.getApprovalIds(), approverId, request.getResult(), request.getComment());
        
        return success ? FrResult.success() : FrResult.failed("批量审核失败");
    }

    @GetMapping("/pending")
    @Operation(summary = "查询待审核记录")
    public FrResult<List<InspectionApproval>> getPendingApprovals() {
        String approverId = SecurityUtils.getLoginUserId();
        List<InspectionApproval> approvals = approvalService.getPendingApprovals(approverId);
        return FrResult.success(approvals);
    }

    @GetMapping("/approved")
    @Operation(summary = "查询已审核记录")
    public FrResult<List<InspectionApproval>> getApprovedRecords() {
        String approverId = SecurityUtils.getLoginUserId();
        List<InspectionApproval> approvals = approvalService.getApprovedRecords(approverId);
        return FrResult.success(approvals);
    }

    @GetMapping("/task/{inspectionTaskId}")
    @Operation(summary = "根据任务ID查询审核记录")
    public FrResult<InspectionApproval> getByTaskId(
            @Parameter(description = "点检任务ID") @PathVariable @NotBlank String inspectionTaskId) {
        
        InspectionApproval approval = approvalService.getByTaskId(inspectionTaskId);
        return FrResult.success(approval);
    }

    @GetMapping("/history/{assetId}")
    @Operation(summary = "查询审核历史")
    public FrResult<List<InspectionApproval>> getApprovalHistory(
            @Parameter(description = "设备ID") @PathVariable @NotBlank String assetId,
            @Parameter(description = "开始时间") @RequestParam(required = false) LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) LocalDateTime endTime) {
        
        List<InspectionApproval> history = approvalService.getApprovalHistory(assetId, startTime, endTime);
        return FrResult.success(history);
    }

    @GetMapping("/statistics")
    @Operation(summary = "统计审核数据")
    public FrResult<Map<String, Object>> getApprovalStatistics(
            @Parameter(description = "开始时间") @RequestParam(required = false) LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) LocalDateTime endTime,
            @Parameter(description = "部门ID") @RequestParam(required = false) String deptId) {
        
        Map<String, Object> params = Map.of(
            "startTime", startTime,
            "endTime", endTime,
            "deptId", deptId != null ? deptId : ""
        );
        
        Map<String, Object> statistics = approvalService.getApprovalStatistics(params);
        return FrResult.success(statistics);
    }

    @GetMapping("/abnormal")
    @Operation(summary = "查询异常审核记录")
    public FrResult<List<InspectionApproval>> getAbnormalApprovals() {
        List<InspectionApproval> approvals = approvalService.getAbnormalApprovals();
        return FrResult.success(approvals);
    }

    @GetMapping("/quality/distribution")
    @Operation(summary = "统计质量等级分布")
    public FrResult<List<Map<String, Object>>> getQualityDistribution(
            @Parameter(description = "开始时间") @RequestParam(required = false) LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) LocalDateTime endTime) {
        
        Map<String, Object> params = Map.of(
            "startTime", startTime,
            "endTime", endTime
        );
        
        List<Map<String, Object>> distribution = approvalService.getQualityDistribution(params);
        return FrResult.success(distribution);
    }

    @GetMapping("/workload")
    @Operation(summary = "查询审核工作量统计")
    public FrResult<List<Map<String, Object>>> getApprovalWorkload(
            @Parameter(description = "开始时间") @RequestParam LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam LocalDateTime endTime) {
        
        List<Map<String, Object>> workload = approvalService.getApprovalWorkload(startTime, endTime);
        return FrResult.success(workload);
    }

    @PostMapping("/export")
    @Operation(summary = "导出审核记录")
    public FrResult<Void> exportApprovalRecords(@RequestBody Map<String, Object> params) {
        String exportType = (String) params.getOrDefault("exportType", "EXCEL");
        approvalService.exportApprovalRecords(params, exportType);
        return FrResult.success();
    }

    @GetMapping("/report")
    @Operation(summary = "生成审核报告")
    public FrResult<Map<String, Object>> generateApprovalReport(
            @Parameter(description = "开始时间") @RequestParam(required = false) LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) LocalDateTime endTime,
            @Parameter(description = "部门ID") @RequestParam(required = false) String deptId) {
        
        Map<String, Object> params = Map.of(
            "startTime", startTime,
            "endTime", endTime,
            "deptId", deptId != null ? deptId : ""
        );
        
        Map<String, Object> report = approvalService.generateApprovalReport(params);
        return FrResult.success(report);
    }

    // ==================== 内部类 ====================

    /**
     * 审核请求
     */
    public static class ApprovalRequest {
        @NotBlank(message = "审核ID不能为空")
        private String approvalId;
        
        private String comment;
        private String reason;

        // getters and setters
        public String getApprovalId() { return approvalId; }
        public void setApprovalId(String approvalId) { this.approvalId = approvalId; }
        public String getComment() { return comment; }
        public void setComment(String comment) { this.comment = comment; }
        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
    }

    /**
     * 批量审核请求
     */
    public static class BatchApprovalRequest {
        @NotEmpty(message = "审核ID列表不能为空")
        private List<String> approvalIds;
        
        @NotBlank(message = "审核结果不能为空")
        private String result;
        
        private String comment;

        // getters and setters
        public List<String> getApprovalIds() { return approvalIds; }
        public void setApprovalIds(List<String> approvalIds) { this.approvalIds = approvalIds; }
        public String getResult() { return result; }
        public void setResult(String result) { this.result = result; }
        public String getComment() { return comment; }
        public void setComment(String comment) { this.comment = comment; }
    }
}