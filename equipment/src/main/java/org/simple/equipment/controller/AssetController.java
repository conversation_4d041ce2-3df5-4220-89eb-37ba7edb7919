package org.simple.equipment.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.simple.base.dto.PageResult;
import org.simple.equipment.entity.Asset;
import org.simple.equipment.service.AssetService;

import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;

/**
 * Asset Controller
 *
 * <AUTHOR> System
 * @date 2025-08-16
 */
@RestController
@RequestMapping("/equipment/asset")
public class AssetController {

    @Autowired
    private AssetService assetService;

    /**
     * 获取设备列表
     */
    @GetMapping("/list")
    public PageResult<Asset> list(@RequestParam(defaultValue = "1") Integer current,
                                        @RequestParam(defaultValue = "10") Integer size) {
        Page<Asset> page = new Page<>(current, size);
        Page<Asset> result = assetService.page(page);
        return PageResult.<Asset>builder()
                .records(result.getRecords())
                .total(result.getTotal())
                .current(result.getCurrent())
                .size(result.getSize())
                .build();
    }

    /**
     * 获取设备详情
     */
    @GetMapping("/{id}")
    public Asset getById(@PathVariable String id) {
        return assetService.getById(id);
    }

    /**
     * 创建新设备
     */
    @PostMapping
    public boolean create(@Valid @RequestBody Asset asset) {
        return assetService.save(asset);
    }

    /**
     * 更新设备信息
     */
    @PutMapping("/{id}")
    public boolean update(@PathVariable String id, @Valid @RequestBody Asset asset) {
        asset.setId(id);
        return assetService.updateById(asset);
    }

    /**
     * 删除设备
     */
    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return assetService.removeById(id);
    }
}