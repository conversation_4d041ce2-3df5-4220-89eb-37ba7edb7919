package org.simple.equipment.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.simple.base.vo.FrResult;
import org.simple.equipment.dto.OfflineDataPackage;
import org.simple.equipment.dto.SyncConflictResolution;
import org.simple.equipment.dto.SyncRequest;
import org.simple.equipment.dto.SyncResponse;
import org.simple.equipment.entity.OfflineSyncRecord;
import org.simple.equipment.service.OfflineSyncService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 离线同步控制器
 * 
 * <AUTHOR>
 */
@Tag(name = "离线同步管理", description = "移动端离线数据同步相关接口")
@RestController
@RequestMapping("/api/equipment/offline-sync")
@RequiredArgsConstructor
@Validated
public class OfflineSyncController {

    private final OfflineSyncService offlineSyncService;

    @Operation(summary = "下载离线数据包", description = "为移动端下载离线工作所需的数据包")
    @PostMapping("/download")
    public FrResult<OfflineDataPackage> downloadOfflineData(
            @RequestParam String userId,
            @RequestParam String deviceId,
            @RequestParam List<String> dataTypes) {
        
        OfflineDataPackage dataPackage = offlineSyncService.downloadOfflineData(userId, deviceId, dataTypes);
        return FrResult.success(dataPackage);
    }

    @Operation(summary = "上传同步数据", description = "上传移动端离线操作的数据到服务器")
    @PostMapping("/upload")
    public FrResult<SyncResponse> uploadSyncData(@Valid @RequestBody SyncRequest request) {
        SyncResponse response = offlineSyncService.uploadSyncData(request);
        return FrResult.success(response);
    }

    @Operation(summary = "检测并解决冲突", description = "检测数据冲突并尝试自动解决")
    @PostMapping("/resolve-conflict/{syncRecordId}")
    public FrResult<SyncConflictResolution> resolveConflict(@PathVariable String syncRecordId) {
        SyncConflictResolution resolution = offlineSyncService.detectAndResolveConflicts(syncRecordId);
        return FrResult.success(resolution);
    }

    @Operation(summary = "获取待同步记录", description = "查询用户设备的待同步数据记录")
    @GetMapping("/pending")
    public FrResult<List<OfflineSyncRecord>> getPendingSyncRecords(
            @RequestParam String userId,
            @RequestParam String deviceId) {
        
        List<OfflineSyncRecord> records = offlineSyncService.getPendingSyncRecords(userId, deviceId);
        return FrResult.success(records);
    }

    @Operation(summary = "批量同步数据", description = "批量同步用户设备的所有待同步数据")
    @PostMapping("/batch-sync")
    public FrResult<SyncResponse> batchSyncData(
            @RequestParam String userId,
            @RequestParam String deviceId) {
        
        SyncResponse response = offlineSyncService.batchSyncData(userId, deviceId);
        return FrResult.success(response);
    }

    @Operation(summary = "获取同步状态", description = "查询用户设备的数据同步状态")
    @GetMapping("/status")
    public FrResult<SyncResponse> getSyncStatus(
            @RequestParam String userId,
            @RequestParam String deviceId) {
        
        SyncResponse response = offlineSyncService.getSyncStatus(userId, deviceId);
        return FrResult.success(response);
    }

    @Operation(summary = "强制同步实体", description = "强制同步指定的数据实体")
    @PostMapping("/force-sync")
    public FrResult<Boolean> forceSyncEntity(
            @RequestParam String entityId,
            @RequestParam String entityType,
            @RequestParam String userId) {
        
        Boolean result = offlineSyncService.forceSyncEntity(entityId, entityType, userId);
        return FrResult.success(result);
    }

    @Operation(summary = "重置同步状态", description = "重置用户设备的同步状态")
    @PostMapping("/reset")
    public FrResult<Boolean> resetSyncStatus(
            @RequestParam String userId,
            @RequestParam String deviceId) {
        
        Boolean result = offlineSyncService.resetSyncStatus(userId, deviceId);
        return FrResult.success(result);
    }

    @Operation(summary = "清理过期缓存", description = "清理系统中的过期缓存数据")
    @PostMapping("/clean-cache")
    public FrResult<Integer> cleanExpiredCache() {
        Integer count = offlineSyncService.cleanExpiredCache();
        return FrResult.success(count);
    }

    @Operation(summary = "快速离线准备", description = "护士快速准备离线工作的简化接口")
    @PostMapping("/quick-prepare")
    public FrResult<OfflineDataPackage> quickPrepareOffline(
            @RequestParam String userId,
            @RequestParam String deviceId,
            @RequestParam(required = false, defaultValue = "8") Integer workHours) {
        
        // 默认下载所有必要的数据类型
        List<String> dataTypes = List.of("TASKS", "TEMPLATES", "EQUIPMENT", "SETTINGS");
        
        OfflineDataPackage dataPackage = offlineSyncService.downloadOfflineData(userId, deviceId, dataTypes);
        
        // 设置工作时长的过期时间
        long workDuration = workHours * 60 * 60 * 1000L; // 转换为毫秒
        dataPackage.setExpiryTime(System.currentTimeMillis() + workDuration);
        
        return FrResult.success(dataPackage);
    }

    @Operation(summary = "网络恢复后快速同步", description = "网络恢复后快速同步所有离线数据")
    @PostMapping("/network-recovery-sync")
    public FrResult<SyncResponse> networkRecoverySync(
            @RequestParam String userId,
            @RequestParam String deviceId,
            @RequestParam(required = false, defaultValue = "false") Boolean forceSync) {
        
        if (forceSync) {
            // 重置失败状态后再同步
            offlineSyncService.resetSyncStatus(userId, deviceId);
        }
        
        SyncResponse response = offlineSyncService.batchSyncData(userId, deviceId);
        return FrResult.success(response);
    }
}