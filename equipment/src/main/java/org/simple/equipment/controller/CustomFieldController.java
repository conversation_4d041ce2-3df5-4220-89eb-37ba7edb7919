package org.simple.equipment.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.simple.base.vo.FrResult;
import org.simple.equipment.entity.CustomField;
import org.simple.equipment.service.CustomFieldService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 设备自定义字段配置控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/equipment/customField")
@Tag(name = "设备自定义字段管理", description = "设备自定义字段管理")
public class CustomFieldController {

    @Resource
    private CustomFieldService customFieldService;

    /**
     * 查询自定义字段列表
     */
    @GetMapping("/list")
    @SaCheckPermission("equipment:customField:query")
    @Operation(summary = "查询自定义字段列表")
    public FrResult<List<CustomField>> list(CustomField customField) {
        List<CustomField> list = customFieldService.getCustomFieldList(customField);
        return FrResult.success(list);
    }

    /**
     * 查询有效的自定义字段列表
     */
    @GetMapping("/activeList")
    @SaCheckPermission("equipment:customField:query")
    @Operation(summary = "查询有效的自定义字段列表")
    public FrResult<List<CustomField>> activeList() {
        List<CustomField> list = customFieldService.getActiveFieldList();
        return FrResult.success(list);
    }

    /**
     * 根据设备分类查询自定义字段
     */
    @GetMapping("/listByCategory")
    @SaCheckPermission("equipment:customField:query")
    @Operation(summary = "根据设备分类查询自定义字段")
    public FrResult<List<CustomField>> listByCategory(@RequestParam(required = false) String categoryId,
                                                      @RequestParam(required = false) String typeId) {
        List<CustomField> list = customFieldService.getFieldsByCategory(categoryId, typeId);
        return FrResult.success(list);
    }

    /**
     * 根据分组查询字段
     */
    @GetMapping("/listByGroup")
    @SaCheckPermission("equipment:customField:query")
    @Operation(summary = "根据分组查询字段")
    public FrResult<List<CustomField>> listByGroup(@RequestParam String fieldGroup) {
        List<CustomField> list = customFieldService.getFieldsByGroup(fieldGroup);
        return FrResult.success(list);
    }

    /**
     * 查询自定义字段详情
     */
    @GetMapping("/detail/{id}")
    @SaCheckPermission("equipment:customField:query")
    @Operation(summary = "查询自定义字段详情")
    public FrResult<CustomField> detail(@PathVariable String id) {
        CustomField customField = customFieldService.getById(id);
        return customField != null ? FrResult.success(customField) : FrResult.failed("字段不存在");
    }

    /**
     * 新增自定义字段
     */
    @PostMapping("/add")
    @SaCheckPermission("equipment:customField:add")
    @Operation(summary = "新增自定义字段")
    public FrResult<?> add(@RequestBody @Validated CustomField customField) {
        return customFieldService.saveCustomField(customField);
    }

    /**
     * 修改自定义字段
     */
    @PostMapping("/edit")
    @SaCheckPermission("equipment:customField:edit")
    @Operation(summary = "修改自定义字段")
    public FrResult<?> edit(@RequestBody @Validated CustomField customField) {
        if (StrUtil.isBlank(customField.getId())) {
            return FrResult.failed("字段ID不能为空");
        }
        return customFieldService.updateCustomField(customField);
    }

    /**
     * 删除自定义字段
     */
    @DeleteMapping("/del/{id}")
    @SaCheckPermission("equipment:customField:del")
    @Operation(summary = "删除自定义字段")
    public FrResult<?> delete(@PathVariable String id) {
        return customFieldService.deleteCustomField(id);
    }

    /**
     * 检查字段编码是否存在
     */
    @GetMapping("/checkFieldCode")
    @SaCheckPermission("equipment:customField:query")
    @Operation(summary = "检查字段编码是否存在")
    public FrResult<Boolean> checkFieldCode(@RequestParam String fieldCode,
                                           @RequestParam(required = false) String excludeId) {
        boolean exists = customFieldService.checkFieldCodeExists(fieldCode, excludeId);
        return FrResult.success(exists);
    }

    /**
     * 更新字段状态
     */
    @PostMapping("/updateStatus")
    @SaCheckPermission("equipment:customField:edit")
    @Operation(summary = "更新字段状态")
    public FrResult<?> updateStatus(@RequestParam String id,
                                    @RequestParam String isEnabled) {
        return customFieldService.updateFieldStatus(id, isEnabled);
    }

    /**
     * 复制字段配置
     */
    @PostMapping("/copyFields")
    @SaCheckPermission("equipment:customField:add")
    @Operation(summary = "复制字段配置")
    public FrResult<?> copyFields(@RequestParam String fromCategoryId,
                                  @RequestParam String toCategoryId) {
        return customFieldService.copyFields(fromCategoryId, toCategoryId);
    }

    /**
     * 获取字段类型选项
     */
    @GetMapping("/fieldTypes")
    @SaCheckPermission("equipment:customField:query")
    @Operation(summary = "获取字段类型选项")
    public FrResult<List<Object>> fieldTypes() {
        List<Object> fieldTypes = List.of(
            new Object[]{"text", "文本"},
            new Object[]{"number", "数字"},
            new Object[]{"date", "日期"},
            new Object[]{"datetime", "日期时间"},
            new Object[]{"select", "下拉选择"},
            new Object[]{"radio", "单选按钮"},
            new Object[]{"checkbox", "多选框"},
            new Object[]{"textarea", "多行文本"},
            new Object[]{"file", "文件上传"},
            new Object[]{"switch", "开关"}
        );
        return FrResult.success(fieldTypes);
    }
}