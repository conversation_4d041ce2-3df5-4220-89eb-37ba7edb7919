package org.simple.equipment.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.simple.base.vo.FrResult;
import org.simple.equipment.entity.PatrolTask;
import org.simple.equipment.service.PatrolTaskService;

import java.util.List;
import java.util.Map;

/**
 * 设备巡检任务控制器
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
@Slf4j
@RestController
@RequestMapping("/equipment/patrol/task")
@Tag(name = "设备巡检任务", description = "设备巡检任务管理接口")
public class PatrolTaskController {

    @Autowired
    private PatrolTaskService patrolTaskService;

    @GetMapping("/page")
    @Operation(summary = "分页查询巡检任务")
    public FrResult<IPage<PatrolTask>> selectPatrolTaskPage(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "10") Integer pageSize,
            @Parameter(description = "查询条件") PatrolTask task) {
        Page<PatrolTask> page = new Page<>(pageNum, pageSize);
        IPage<PatrolTask> taskPage = patrolTaskService.selectPatrolTaskPage(page, task);
        return FrResult.success(taskPage);
    }

    @GetMapping("/list")
    @Operation(summary = "查询巡检任务列表")
    public FrResult<List<PatrolTask>> getPatrolTaskList(PatrolTask task) {
        List<PatrolTask> tasks = patrolTaskService.list();
        return FrResult.success(tasks);
    }

    @GetMapping("/{taskId}")
    @Operation(summary = "获取巡检任务详情")
    public FrResult<PatrolTask> getPatrolTaskById(
            @Parameter(description = "任务ID") @PathVariable String taskId) {
        PatrolTask task = patrolTaskService.getById(taskId);
        return FrResult.success(task);
    }

    @PostMapping
    @Operation(summary = "创建巡检任务")
    public FrResult<Boolean> createPatrolTask(@RequestBody PatrolTask task) {
        boolean result = patrolTaskService.createPatrolTask(task);
        return FrResult.success(result);
    }

    @PutMapping
    @Operation(summary = "更新巡检任务")
    public FrResult<Boolean> updatePatrolTask(@RequestBody PatrolTask task) {
        boolean result = patrolTaskService.updatePatrolTask(task);
        return FrResult.success(result);
    }

    @PostMapping("/assign/{taskId}")
    @Operation(summary = "分配巡检任务")
    public FrResult<Boolean> assignTask(
            @Parameter(description = "任务ID") @PathVariable String taskId,
            @Parameter(description = "执行人ID") @RequestParam String executorId,
            @Parameter(description = "分配人ID") @RequestParam String assignedBy) {
        boolean result = patrolTaskService.assignTask(taskId, executorId, assignedBy);
        return FrResult.success(result);
    }

    @PostMapping("/accept/{taskId}")
    @Operation(summary = "接受巡检任务")
    public FrResult<Boolean> acceptTask(
            @Parameter(description = "任务ID") @PathVariable String taskId,
            @Parameter(description = "执行人ID") @RequestParam String executorId) {
        boolean result = patrolTaskService.acceptTask(taskId, executorId);
        return FrResult.success(result);
    }

    @PostMapping("/start/{taskId}")
    @Operation(summary = "开始执行任务")
    public FrResult<Boolean> startTask(
            @Parameter(description = "任务ID") @PathVariable String taskId,
            @Parameter(description = "执行人ID") @RequestParam String executorId) {
        boolean result = patrolTaskService.startTask(taskId, executorId);
        return FrResult.success(result);
    }

    @PostMapping("/complete/{taskId}")
    @Operation(summary = "完成巡检任务")
    public FrResult<Boolean> completeTask(
            @Parameter(description = "任务ID") @PathVariable String taskId,
            @Parameter(description = "执行人ID") @RequestParam String executorId,
            @Parameter(description = "执行结果") @RequestParam(required = false) String executionResult) {
        boolean result = patrolTaskService.completeTask(taskId, executorId, executionResult);
        return FrResult.success(result);
    }

    @PostMapping("/cancel/{taskId}")
    @Operation(summary = "取消巡检任务")
    public FrResult<Boolean> cancelTask(
            @Parameter(description = "任务ID") @PathVariable String taskId,
            @Parameter(description = "取消原因") @RequestParam String cancelReason,
            @Parameter(description = "取消人ID") @RequestParam String cancelBy) {
        boolean result = patrolTaskService.cancelTask(taskId, cancelReason, cancelBy);
        return FrResult.success(result);
    }

    @PutMapping("/progress/{taskId}")
    @Operation(summary = "更新任务进度")
    public FrResult<Boolean> updateTaskProgress(
            @Parameter(description = "任务ID") @PathVariable String taskId,
            @Parameter(description = "已完成要点数") @RequestParam Integer completedCheckpoints) {
        boolean result = patrolTaskService.updateTaskProgress(taskId, completedCheckpoints);
        return FrResult.success(result);
    }

    @GetMapping("/user/{userId}")
    @Operation(summary = "查询用户的巡检任务")
    public FrResult<List<PatrolTask>> getUserPatrolTasks(
            @Parameter(description = "用户ID") @PathVariable String userId,
            @Parameter(description = "任务状态") @RequestParam(required = false) String status) {
        List<PatrolTask> tasks = patrolTaskService.getUserPatrolTasks(userId, status);
        return FrResult.success(tasks);
    }

    @GetMapping("/dept/{deptId}")
    @Operation(summary = "查询部门的巡检任务")
    public FrResult<List<PatrolTask>> getDeptPatrolTasks(
            @Parameter(description = "部门ID") @PathVariable String deptId,
            @Parameter(description = "任务状态") @RequestParam(required = false) String status) {
        List<PatrolTask> tasks = patrolTaskService.getDeptPatrolTasks(deptId, status);
        return FrResult.success(tasks);
    }

    @GetMapping("/upcoming")
    @Operation(summary = "查询即将到期的任务")
    public FrResult<List<PatrolTask>> getUpcomingTasks(
            @Parameter(description = "小时数") @RequestParam(defaultValue = "24") Integer hours) {
        List<PatrolTask> tasks = patrolTaskService.getUpcomingTasks(hours);
        return FrResult.success(tasks);
    }

    @GetMapping("/overdue")
    @Operation(summary = "查询超时任务")
    public FrResult<List<PatrolTask>> getOverdueTasks() {
        List<PatrolTask> tasks = patrolTaskService.getOverdueTasks();
        return FrResult.success(tasks);
    }

    @GetMapping("/statistics")
    @Operation(summary = "获取任务统计")
    public FrResult<Map<String, Object>> getTaskStatistics(
            @Parameter(description = "用户ID") @RequestParam(required = false) String userId,
            @Parameter(description = "部门ID") @RequestParam(required = false) String deptId) {
        Map<String, Object> statistics = patrolTaskService.getTaskStatistics(userId, deptId);
        return FrResult.success(statistics);
    }

    @GetMapping("/execution-details/{taskId}")
    @Operation(summary = "获取任务执行详情")
    public FrResult<Map<String, Object>> getTaskExecutionDetails(
            @Parameter(description = "任务ID") @PathVariable String taskId) {
        Map<String, Object> details = patrolTaskService.getTaskExecutionDetails(taskId);
        return FrResult.success(details);
    }

    @PostMapping("/batch-assign")
    @Operation(summary = "批量分配任务")
    public FrResult<Map<String, Object>> batchAssignTasks(
            @Parameter(description = "任务ID列表") @RequestParam List<String> taskIds,
            @Parameter(description = "执行人ID") @RequestParam String executorId,
            @Parameter(description = "分配人ID") @RequestParam String assignedBy) {
        Map<String, Object> result = patrolTaskService.batchAssignTasks(taskIds, executorId, assignedBy);
        return FrResult.success(result);
    }

    @PostMapping("/check-overdue")
    @Operation(summary = "检查并更新超时任务")
    public FrResult<Integer> checkAndUpdateOverdueTasks() {
        int updatedCount = patrolTaskService.checkAndUpdateOverdueTasks();
        return FrResult.success(updatedCount);
    }

    @GetMapping("/report/{taskId}")
    @Operation(summary = "生成任务报告")
    public FrResult<Map<String, Object>> generateTaskReport(
            @Parameter(description = "任务ID") @PathVariable String taskId) {
        Map<String, Object> report = patrolTaskService.generateTaskReport(taskId);
        return FrResult.success(report);
    }

    @GetMapping("/export")
    @Operation(summary = "导出任务数据")
    public FrResult<List<Map<String, Object>>> exportTaskData(
            @Parameter(description = "任务ID列表") @RequestParam(required = false) List<String> taskIds) {
        List<Map<String, Object>> result = patrolTaskService.exportTaskData(taskIds);
        return FrResult.success(result);
    }
}
