package org.simple.equipment.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.simple.base.vo.FrResult;
import org.simple.equipment.entity.AssetVersion;
import org.simple.equipment.service.AssetVersionService;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 设备档案版本控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/equipment/assetVersion")
@Tag(name = "设备档案版本管理", description = "设备档案版本管理")
public class AssetVersionController {

    @Resource
    private AssetVersionService assetVersionService;

    /**
     * 查询设备的版本列表
     */
    @GetMapping("/list/{assetId}")
    @SaCheckPermission("equipment:asset:query")
    @Operation(summary = "查询设备的版本列表")
    public FrResult<List<AssetVersion>> list(@PathVariable String assetId) {
        List<AssetVersion> list = assetVersionService.getVersionsByAssetId(assetId);
        return FrResult.success(list);
    }

    /**
     * 查询设备的当前版本
     */
    @GetMapping("/current/{assetId}")
    @SaCheckPermission("equipment:asset:query")
    @Operation(summary = "查询设备的当前版本")
    public FrResult<AssetVersion> current(@PathVariable String assetId) {
        AssetVersion current = assetVersionService.getCurrentVersion(assetId);
        return FrResult.success(current);
    }

    /**
     * 查询版本详情
     */
    @GetMapping("/detail/{versionId}")
    @SaCheckPermission("equipment:asset:query")
    @Operation(summary = "查询版本详情")
    public FrResult<Map<String, Object>> detail(@PathVariable String versionId) {
        Map<String, Object> detail = assetVersionService.getVersionDetail(versionId);
        return FrResult.success(detail);
    }

    /**
     * 查询待审批的版本列表
     */
    @GetMapping("/pendingApproval")
    @SaCheckPermission("equipment:asset:approve")
    @Operation(summary = "查询待审批的版本列表")
    public FrResult<List<Map<String, Object>>> pendingApproval(@RequestParam(required = false) String approver) {
        List<Map<String, Object>> list = assetVersionService.getPendingApprovalVersions(approver);
        return FrResult.success(list);
    }

    /**
     * 创建新版本
     */
    @PostMapping("/create")
    @SaCheckPermission("equipment:asset:edit")
    @Operation(summary = "创建新版本")
    public FrResult<?> create(@RequestBody Map<String, Object> request) {
        String assetId = (String) request.get("assetId");
        String versionName = (String) request.get("versionName");
        String versionDesc = (String) request.get("versionDesc");
        String changeSummary = (String) request.get("changeSummary");
        Map<String, Object> assetData = (Map<String, Object>) request.get("assetData");
        List<Map<String, Object>> changeDetails = (List<Map<String, Object>>) request.get("changeDetails");

        return assetVersionService.createVersion(assetId, versionName, versionDesc, 
                                                changeSummary, assetData, changeDetails);
    }

    /**
     * 提交版本审批
     */
    @PostMapping("/submitApproval")
    @SaCheckPermission("equipment:asset:edit")
    @Operation(summary = "提交版本审批")
    public FrResult<?> submitApproval(@RequestParam String versionId,
                                      @RequestParam List<String> approvers) {
        return assetVersionService.submitForApproval(versionId, approvers);
    }

    /**
     * 审批版本
     */
    @PostMapping("/approve")
    @SaCheckPermission("equipment:asset:approve")
    @Operation(summary = "审批版本")
    public FrResult<?> approve(@RequestParam String versionId,
                               @RequestParam String approvalStatus,
                               @RequestParam(required = false) String approvalComment) {
        return assetVersionService.approveVersion(versionId, approvalStatus, approvalComment);
    }

    /**
     * 回滚到指定版本
     */
    @PostMapping("/rollback")
    @SaCheckPermission("equipment:asset:edit")
    @Operation(summary = "回滚到指定版本")
    public FrResult<?> rollback(@RequestParam String assetId,
                                @RequestParam String versionId,
                                @RequestParam String rollbackReason) {
        return assetVersionService.rollbackToVersion(assetId, versionId, rollbackReason);
    }

    /**
     * 设置当前版本
     */
    @PostMapping("/setCurrent/{versionId}")
    @SaCheckPermission("equipment:asset:edit")
    @Operation(summary = "设置当前版本")
    public FrResult<?> setCurrent(@PathVariable String versionId) {
        return assetVersionService.setCurrentVersion(versionId);
    }

    /**
     * 查询版本对比数据
     */
    @GetMapping("/compare")
    @SaCheckPermission("equipment:asset:query")
    @Operation(summary = "查询版本对比数据")
    public FrResult<Map<String, Object>> compare(@RequestParam String fromVersionId,
                                                  @RequestParam String toVersionId) {
        Map<String, Object> compare = assetVersionService.compareVersions(fromVersionId, toVersionId);
        return FrResult.success(compare);
    }

    /**
     * 删除版本
     */
    @DeleteMapping("/delete/{versionId}")
    @SaCheckPermission("equipment:asset:delete")
    @Operation(summary = "删除版本")
    public FrResult<?> delete(@PathVariable String versionId) {
        return assetVersionService.deleteVersion(versionId);
    }

    /**
     * 归档版本
     */
    @PostMapping("/archive/{versionId}")
    @SaCheckPermission("equipment:asset:edit")
    @Operation(summary = "归档版本")
    public FrResult<?> archive(@PathVariable String versionId) {
        return assetVersionService.archiveVersion(versionId);
    }

    /**
     * 批量归档版本
     */
    @PostMapping("/batchArchive")
    @SaCheckPermission("equipment:asset:edit")
    @Operation(summary = "批量归档版本")
    public FrResult<?> batchArchive(@RequestParam List<String> versionIds) {
        return assetVersionService.batchArchiveVersions(versionIds);
    }

    /**
     * 添加版本标签
     */
    @PostMapping("/addTag")
    @SaCheckPermission("equipment:asset:edit")
    @Operation(summary = "添加版本标签")
    public FrResult<?> addTag(@RequestParam String versionId,
                              @RequestParam String tagName,
                              @RequestParam(required = false) String tagValue,
                              @RequestParam(required = false) String tagColor) {
        return assetVersionService.addVersionTag(versionId, tagName, tagValue, tagColor);
    }

    /**
     * 删除版本标签
     */
    @DeleteMapping("/removeTag")
    @SaCheckPermission("equipment:asset:edit")
    @Operation(summary = "删除版本标签")
    public FrResult<?> removeTag(@RequestParam String versionId,
                                 @RequestParam String tagName) {
        return assetVersionService.removeVersionTag(versionId, tagName);
    }

    /**
     * 查询版本统计信息
     */
    @GetMapping("/statistics")
    @SaCheckPermission("equipment:asset:query")
    @Operation(summary = "查询版本统计信息")
    public FrResult<Map<String, Object>> statistics() {
        Map<String, Object> statistics = assetVersionService.getVersionStatistics();
        return FrResult.success(statistics);
    }

    /**
     * 导出版本数据
     */
    @GetMapping("/export/{versionId}")
    @SaCheckPermission("equipment:asset:export")
    @Operation(summary = "导出版本数据")
    public FrResult<Map<String, Object>> export(@PathVariable String versionId) {
        Map<String, Object> data = assetVersionService.exportVersion(versionId);
        return FrResult.success(data);
    }

    /**
     * 清理过期版本
     */
    @PostMapping("/cleanup")
    @SaCheckPermission("equipment:asset:delete")
    @Operation(summary = "清理过期版本")
    public FrResult<?> cleanup(@RequestParam(defaultValue = "365") int retentionDays) {
        return assetVersionService.cleanupExpiredVersions(retentionDays);
    }

    /**
     * 生成版本号
     */
    @GetMapping("/generateVersionNumber/{assetId}")
    @SaCheckPermission("equipment:asset:query")
    @Operation(summary = "生成版本号")
    public FrResult<String> generateVersionNumber(@PathVariable String assetId) {
        String versionNumber = assetVersionService.generateVersionNumber(assetId);
        return FrResult.success(versionNumber);
    }
}