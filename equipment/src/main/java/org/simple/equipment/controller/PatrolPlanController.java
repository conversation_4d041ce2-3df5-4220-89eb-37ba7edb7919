package org.simple.equipment.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.simple.base.vo.FrResult;
import org.simple.equipment.entity.PatrolPlan;
import org.simple.equipment.service.PatrolPlanService;

import java.util.List;
import java.util.Map;

/**
 * 设备巡检计划控制器
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
@Slf4j
@RestController
@RequestMapping("/equipment/patrol/plan")
@Tag(name = "设备巡检计划", description = "设备巡检计划管理接口")
public class PatrolPlanController {

    @Autowired
    private PatrolPlanService patrolPlanService;

    @GetMapping("/page")
    @Operation(summary = "分页查询巡检计划")
    public FrResult<IPage<PatrolPlan>> selectPatrolPlanPage(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "10") Integer pageSize,
            @Parameter(description = "查询条件") PatrolPlan plan) {
        Page<PatrolPlan> page = new Page<>(pageNum, pageSize);
        IPage<PatrolPlan> planPage = patrolPlanService.selectPatrolPlanPage(page, plan);
        return FrResult.success(planPage);
    }

    @GetMapping("/list")
    @Operation(summary = "查询巡检计划列表")
    public FrResult<List<PatrolPlan>> getPatrolPlanList(PatrolPlan plan) {
        List<PatrolPlan> plans = patrolPlanService.list();
        return FrResult.success(plans);
    }

    @GetMapping("/{planId}")
    @Operation(summary = "获取巡检计划详情")
    public FrResult<PatrolPlan> getPatrolPlanById(
            @Parameter(description = "计划ID") @PathVariable String planId) {
        PatrolPlan plan = patrolPlanService.getById(planId);
        return FrResult.success(plan);
    }

    @PostMapping
    @Operation(summary = "创建巡检计划")
    public FrResult<Boolean> createPatrolPlan(@RequestBody PatrolPlan plan) {
        boolean result = patrolPlanService.createPatrolPlan(plan);
        return FrResult.success(result);
    }

    @PutMapping
    @Operation(summary = "更新巡检计划")
    public FrResult<Boolean> updatePatrolPlan(@RequestBody PatrolPlan plan) {
        boolean result = patrolPlanService.updatePatrolPlan(plan);
        return FrResult.success(result);
    }

    @DeleteMapping("/{planId}")
    @Operation(summary = "删除巡检计划")
    public FrResult<Boolean> deletePatrolPlan(
            @Parameter(description = "计划ID") @PathVariable String planId) {
        boolean result = patrolPlanService.deletePatrolPlan(planId);
        return FrResult.success(result);
    }

    @PutMapping("/status/{planId}")
    @Operation(summary = "更新计划状态")
    public FrResult<Boolean> updatePlanStatus(
            @Parameter(description = "计划ID") @PathVariable String planId,
            @Parameter(description = "状态") @RequestParam Integer status) {
        boolean result = patrolPlanService.updatePlanStatus(planId, status);
        return FrResult.success(result);
    }

    @PostMapping("/copy/{planId}")
    @Operation(summary = "复制巡检计划")
    public FrResult<String> copyPatrolPlan(
            @Parameter(description = "计划ID") @PathVariable String planId,
            @Parameter(description = "新计划名称") @RequestParam String newPlanName) {
        String newPlanId = patrolPlanService.copyPatrolPlan(planId, newPlanName);
        return FrResult.success(newPlanId);
    }

    @PostMapping("/generate-tasks/{planId}")
    @Operation(summary = "根据计划生成任务")
    public FrResult<Integer> generateTasksByPlan(
            @Parameter(description = "计划ID") @PathVariable String planId) {
        int generatedCount = patrolPlanService.generateTasksByPlan(planId);
        return FrResult.success(generatedCount);
    }

    @PostMapping("/batch-generate-tasks")
    @Operation(summary = "批量生成任务")
    public FrResult<Map<String, Object>> batchGenerateTasks(
            @Parameter(description = "计划ID列表") @RequestBody List<String> planIds) {
        Map<String, Object> result = patrolPlanService.batchGenerateTasks(planIds);
        return FrResult.success(result);
    }

    @GetMapping("/statistics")
    @Operation(summary = "获取计划统计")
    public FrResult<Map<String, Object>> getPatrolPlanStatistics() {
        Map<String, Object> statistics = patrolPlanService.getPatrolPlanStatistics();
        return FrResult.success(statistics);
    }

    @GetMapping("/expiring")
    @Operation(summary = "查询即将到期的计划")
    public FrResult<List<PatrolPlan>> getExpiringPlans(
            @Parameter(description = "天数") @RequestParam(defaultValue = "7") Integer days) {
        List<PatrolPlan> plans = patrolPlanService.getExpiringPlans(days);
        return FrResult.success(plans);
    }

    @PostMapping("/validate")
    @Operation(summary = "验证计划配置")
    public FrResult<Map<String, Object>> validatePlanConfiguration(@RequestBody PatrolPlan plan) {
        Map<String, Object> result = patrolPlanService.validatePlanConfiguration(plan);
        return FrResult.success(result);
    }

    @GetMapping("/execution-status/{planId}")
    @Operation(summary = "获取计划执行情况")
    public FrResult<Map<String, Object>> getPlanExecutionStatus(
            @Parameter(description = "计划ID") @PathVariable String planId,
            @Parameter(description = "统计天数") @RequestParam(defaultValue = "30") Integer days) {
        Map<String, Object> status = patrolPlanService.getPlanExecutionStatus(planId, days);
        return FrResult.success(status);
    }

    @PostMapping("/import")
    @Operation(summary = "导入巡检计划")
    public FrResult<Map<String, Object>> importPatrolPlans(
            @Parameter(description = "计划数据") @RequestBody List<Map<String, Object>> planData) {
        Map<String, Object> result = patrolPlanService.importPatrolPlans(planData);
        return FrResult.success(result);
    }

    @GetMapping("/export")
    @Operation(summary = "导出巡检计划")
    public FrResult<List<Map<String, Object>>> exportPatrolPlans(
            @Parameter(description = "计划ID列表") @RequestParam(required = false) List<String> planIds) {
        List<Map<String, Object>> result = patrolPlanService.exportPatrolPlans(planIds);
        return FrResult.success(result);
    }
}
