package org.simple.equipment.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.simple.base.vo.FrResult;
import org.simple.equipment.entity.InspectionTemplateItem;
import org.simple.equipment.service.InspectionTemplateItemService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 设备点检模板项目控制器
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
@Tag(name = "设备点检模板项目管理", description = "设备点检模板项目管理相关接口")
@RestController
@RequestMapping("/api/equipment/inspection-template-item")
@RequiredArgsConstructor
public class InspectionTemplateItemController {

    private final InspectionTemplateItemService inspectionTemplateItemService;

    @Operation(summary = "分页查询模板项目")
    @GetMapping("/list")
    public FrResult<IPage<InspectionTemplateItem>> getItemPage(
            @Parameter(description = "当前页") @RequestParam(defaultValue = "1") Long current,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Long size,
            @Parameter(description = "模板ID") @RequestParam(required = false) String templateId,
            @Parameter(description = "项目名称") @RequestParam(required = false) String itemName,
            @Parameter(description = "项目类型") @RequestParam(required = false) String itemType,
            @Parameter(description = "项目分类") @RequestParam(required = false) String itemCategory) {

        Page<InspectionTemplateItem> page = new Page<>(current, size);
        IPage<InspectionTemplateItem> result = inspectionTemplateItemService.getItemPage(page, templateId, itemName, itemType, itemCategory);
        return FrResult.success(result);
    }

    @Operation(summary = "创建模板项目")
    @PostMapping
    public FrResult<Boolean> createItem(@Valid @RequestBody InspectionTemplateItem item) {
        boolean success = inspectionTemplateItemService.createItem(item);
        return FrResult.success(success);
    }

    @Operation(summary = "更新模板项目")
    @PutMapping
    public FrResult<Boolean> updateItem(@Valid @RequestBody InspectionTemplateItem item) {
        boolean success = inspectionTemplateItemService.updateItem(item);
        return FrResult.success(success);
    }

    @Operation(summary = "删除模板项目")
    @DeleteMapping("/{itemId}")
    public FrResult<Boolean> deleteItem(@PathVariable String itemId) {
        boolean success = inspectionTemplateItemService.deleteItem(itemId);
        return FrResult.success(success);
    }

    @Operation(summary = "根据ID获取项目详情")
    @GetMapping("/{itemId}")
    public FrResult<InspectionTemplateItem> getItemById(@PathVariable String itemId) {
        InspectionTemplateItem item = inspectionTemplateItemService.getItemById(itemId);
        return FrResult.success(item);
    }

    @Operation(summary = "根据模板ID查询项目")
    @GetMapping("/template/{templateId}")
    public FrResult<List<InspectionTemplateItem>> getItemsByTemplate(@PathVariable String templateId) {
        List<InspectionTemplateItem> items = inspectionTemplateItemService.getItemsByTemplate(templateId);
        return FrResult.success(items);
    }

    @Operation(summary = "根据项目类型查询项目")
    @GetMapping("/template/{templateId}/type/{itemType}")
    public FrResult<List<InspectionTemplateItem>> getItemsByType(@PathVariable String templateId, @PathVariable String itemType) {
        List<InspectionTemplateItem> items = inspectionTemplateItemService.getItemsByType(templateId, itemType);
        return FrResult.success(items);
    }

    @Operation(summary = "查询关键项目")
    @GetMapping("/template/{templateId}/key-items")
    public FrResult<List<InspectionTemplateItem>> getKeyItems(@PathVariable String templateId) {
        List<InspectionTemplateItem> items = inspectionTemplateItemService.getKeyItems(templateId);
        return FrResult.success(items);
    }

    @Operation(summary = "检查项目编码是否存在")
    @GetMapping("/check-code")
    public FrResult<Boolean> checkItemCodeExists(
            @Parameter(description = "项目编码") @RequestParam String itemCode,
            @Parameter(description = "模板ID") @RequestParam String templateId,
            @Parameter(description = "排除的项目ID") @RequestParam(required = false) String excludeId) {
        boolean exists = inspectionTemplateItemService.checkItemCodeExists(itemCode, templateId, excludeId);
        return FrResult.success(exists);
    }

    @Operation(summary = "生成项目编码")
    @GetMapping("/generate-code")
    public FrResult<String> generateItemCode(
            @Parameter(description = "模板ID") @RequestParam String templateId,
            @Parameter(description = "项目类型") @RequestParam(required = false) String itemType) {
        String code = inspectionTemplateItemService.generateItemCode(templateId, itemType);
        return FrResult.success(code);
    }

    @Operation(summary = "批量保存项目")
    @PostMapping("/batch-save")
    public FrResult<Boolean> batchSaveItems(
            @Parameter(description = "模板ID") @RequestParam String templateId,
            @RequestBody List<InspectionTemplateItem> items) {
        boolean success = inspectionTemplateItemService.batchSaveItems(templateId, items);
        return FrResult.success(success);
    }

    @Operation(summary = "批量删除项目")
    @DeleteMapping("/template/{templateId}/batch-delete")
    public FrResult<Boolean> batchDeleteItems(@PathVariable String templateId) {
        boolean success = inspectionTemplateItemService.batchDeleteItems(templateId);
        return FrResult.success(success);
    }

    @Operation(summary = "更新项目排序")
    @PostMapping("/{itemId}/sort-order")
    public FrResult<Boolean> updateItemSortOrder(@PathVariable String itemId, @RequestParam Integer sortOrder) {
        boolean success = inspectionTemplateItemService.updateItemSortOrder(itemId, sortOrder);
        return FrResult.success(success);
    }

    @Operation(summary = "复制模板项目")
    @PostMapping("/copy")
    public FrResult<Boolean> copyTemplateItems(
            @Parameter(description = "源模板ID") @RequestParam String sourceTemplateId,
            @Parameter(description = "目标模板ID") @RequestParam String targetTemplateId) {
        boolean success = inspectionTemplateItemService.copyTemplateItems(sourceTemplateId, targetTemplateId);
        return FrResult.success(success);
    }

    @Operation(summary = "验证项目数据")
    @PostMapping("/validate")
    public FrResult<Boolean> validateItemData(@RequestBody InspectionTemplateItem item) {
        boolean valid = inspectionTemplateItemService.validateItemData(item);
        return FrResult.success(valid);
    }

    @Operation(summary = "获取项目统计信息")
    @GetMapping("/template/{templateId}/statistics")
    public FrResult<List<InspectionTemplateItem>> getItemStatistics(@PathVariable String templateId) {
        List<InspectionTemplateItem> statistics = inspectionTemplateItemService.getItemStatistics(templateId);
        return FrResult.success(statistics);
    }
}