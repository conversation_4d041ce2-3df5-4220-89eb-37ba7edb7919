package org.simple.equipment.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.simple.base.dto.PageResult;
import org.simple.equipment.entity.Category;
import org.simple.equipment.service.CategoryService;

import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;

/**
 * Category Controller
 *
 * <AUTHOR> System
 * @date 2025-08-16
 */
@RestController
@RequestMapping("/equipment/category")
public class CategoryController {

    @Autowired
    private CategoryService categoryService;

    /**
     * 获取分类树
     */
    @GetMapping("/list")
    public PageResult<Category> list(@RequestParam(defaultValue = "1") Integer current,
                                        @RequestParam(defaultValue = "10") Integer size) {
        Page<Category> page = new Page<>(current, size);
        Page<Category> result = categoryService.page(page);
        return PageResult.<Category>builder()
                .records(result.getRecords())
                .total(result.getTotal())
                .current(result.getCurrent())
                .size(result.getSize())
                .build();
    }

    /**
     * 创建分类
     */
    @PostMapping
    public boolean create(@Valid @RequestBody Category category) {
        return categoryService.save(category);
    }

    /**
     * 更新分类
     */
    @PutMapping("/{id}")
    public boolean update(@PathVariable String id, @Valid @RequestBody Category category) {
        category.setId(id);
        return categoryService.updateById(category);
    }
}