package org.simple.equipment.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.simple.base.vo.FrResult;
import org.simple.equipment.dto.TemplateAssociationRequest;
import org.simple.equipment.dto.TemplateRecommendationResponse;
import org.simple.equipment.service.IntelligentTemplateService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * 智能模板关联控制器
 * 
 * <AUTHOR>
 */
@Tag(name = "智能模板关联", description = "智能模板推荐和关联管理接口")
@RestController
@RequestMapping("/api/equipment/intelligent-template")
@RequiredArgsConstructor
@Validated
public class IntelligentTemplateController {

    private final IntelligentTemplateService intelligentTemplateService;

    @Operation(summary = "获取设备模板推荐", description = "根据设备ID智能推荐适合的点检模板")
    @GetMapping("/recommend/{equipmentId}")
    public FrResult<TemplateRecommendationResponse> recommendTemplate(
            @PathVariable String equipmentId,
            @RequestParam String userId) {
        
        TemplateRecommendationResponse response = intelligentTemplateService.recommendTemplate(equipmentId, userId);
        return FrResult.success(response);
    }

    @Operation(summary = "自动关联设备与模板", description = "自动建立设备与模板的关联关系")
    @PostMapping("/associate")
    public FrResult<Boolean> autoAssociateTemplate(@Valid @RequestBody TemplateAssociationRequest request) {
        Boolean result = intelligentTemplateService.autoAssociateTemplate(request);
        return FrResult.success(result);
    }

    @Operation(summary = "更新关联优先级", description = "更新设备模板关联的优先级")
    @PutMapping("/priority")
    public FrResult<Void> updateAssociationPriority(
            @RequestParam String equipmentId,
            @RequestParam String templateId,
            @RequestParam Integer priority) {
        
        intelligentTemplateService.updateAssociationPriority(equipmentId, templateId, priority);
        return FrResult.success();
    }

    @Operation(summary = "根据类型推荐模板", description = "根据设备类型和科室推荐模板")
    @GetMapping("/recommend-by-type")
    public FrResult<String> getRecommendedTemplateByType(
            @RequestParam String equipmentType,
            @RequestParam String departmentId) {
        
        String templateId = intelligentTemplateService.getRecommendedTemplateByType(equipmentType, departmentId);
        return FrResult.success(templateId);
    }
}