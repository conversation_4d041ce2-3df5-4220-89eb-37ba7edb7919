package org.simple.equipment.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.simple.base.vo.FrResult;
import org.simple.equipment.dto.ExceptionAnalysisRequest;
import org.simple.equipment.dto.ExceptionAnalysisResponse;
import org.simple.equipment.dto.ExceptionHandlingRequest;
import org.simple.equipment.entity.ExceptionHandlingRecord;
import org.simple.equipment.service.IntelligentExceptionHandlerService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 智能异常处理控制器
 * 
 * <AUTHOR>
 */
@Tag(name = "智能异常处理", description = "智能异常分析、处理和通知相关接口")
@RestController
@RequestMapping("/api/equipment/intelligent-exception")
@RequiredArgsConstructor
@Validated
public class IntelligentExceptionController {

    private final IntelligentExceptionHandlerService exceptionHandlerService;

    @Operation(summary = "智能分析异常", description = "使用AI算法分析异常等级和处理建议")
    @PostMapping("/analyze")
    public FrResult<ExceptionAnalysisResponse> analyzeException(@Valid @RequestBody ExceptionAnalysisRequest request) {
        ExceptionAnalysisResponse response = exceptionHandlerService.analyzeException(request);
        return FrResult.success(response);
    }

    @Operation(summary = "处理异常事件", description = "创建异常处理记录并执行自动化处理流程")
    @PostMapping("/handle")
    public FrResult<ExceptionHandlingRecord> handleException(@Valid @RequestBody ExceptionHandlingRequest request) {
        ExceptionHandlingRecord record = exceptionHandlerService.handleException(request);
        return FrResult.success(record);
    }

    @Operation(summary = "发送异常通知", description = "向相关人员发送异常通知")
    @PostMapping("/notify/{recordId}")
    public FrResult<Boolean> sendNotifications(@PathVariable String recordId) {
        Boolean result = exceptionHandlerService.sendNotifications(recordId);
        return FrResult.success(result);
    }

    @Operation(summary = "生成维修工单", description = "为异常自动生成维修工单")
    @PostMapping("/work-order/{recordId}")
    public FrResult<String> generateWorkOrder(@PathVariable String recordId) {
        String workOrderId = exceptionHandlerService.generateWorkOrder(recordId);
        return FrResult.success(workOrderId);
    }

    @Operation(summary = "异常升级处理", description = "将异常升级到更高处理级别")
    @PostMapping("/escalate/{recordId}")
    public FrResult<Boolean> escalateException(@PathVariable String recordId) {
        Boolean result = exceptionHandlerService.escalateException(recordId);
        return FrResult.success(result);
    }

    @Operation(summary = "获取处理进度", description = "查询异常处理进度和状态")
    @GetMapping("/progress/{recordId}")
    public FrResult<ExceptionHandlingRecord> getHandlingProgress(@PathVariable String recordId) {
        ExceptionHandlingRecord record = exceptionHandlerService.getHandlingProgress(recordId);
        return FrResult.success(record);
    }

    @Operation(summary = "处理超时异常", description = "批量处理所有超时的异常记录")
    @PostMapping("/process-overdue")
    public FrResult<Integer> processOverdueExceptions() {
        Integer count = exceptionHandlerService.processOverdueExceptions();
        return FrResult.success(count);
    }

    @Operation(summary = "获取设备异常历史", description = "查询指定设备的异常处理历史")
    @GetMapping("/history/{equipmentId}")
    public FrResult<List<ExceptionHandlingRecord>> getEquipmentExceptionHistory(
            @PathVariable String equipmentId,
            @RequestParam(defaultValue = "30") Integer days) {
        
        List<ExceptionHandlingRecord> records = exceptionHandlerService.getEquipmentExceptionHistory(equipmentId, days);
        return FrResult.success(records);
    }

    @Operation(summary = "快速异常报告", description = "护士快速报告异常的简化接口")
    @PostMapping("/quick-report")
    public FrResult<ExceptionHandlingRecord> quickExceptionReport(
            @RequestParam String equipmentId,
            @RequestParam String description,
            @RequestParam String reporterId,
            @RequestParam(required = false) String photo) {
        
        ExceptionHandlingRequest request = new ExceptionHandlingRequest();
        request.setEquipmentId(equipmentId);
        request.setExceptionDescription(description);
        request.setDetectionMethod(ExceptionHandlingRecord.DetectionMethod.MANUAL);
        request.setReporterId(reporterId);
        request.setAutoAnalyze(true);
        request.setAutoNotify(true);
        
        if (photo != null) {
            request.setPhotos(List.of(photo));
        }
        
        ExceptionHandlingRecord record = exceptionHandlerService.handleException(request);
        return FrResult.success(record);
    }
}