package org.simple.equipment.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.simple.equipment.entity.InspectionTask;
import org.simple.equipment.service.InspectionTaskService;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * InspectionTaskController 测试类
 *
 * <AUTHOR> System
 * @date 2025-08-16
 */
@WebMvcTest(InspectionTaskController.class)
class InspectionTaskControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private InspectionTaskService inspectiontaskService;

    @Autowired
    private ObjectMapper objectMapper;

    private InspectionTask testInspectionTask;

    @BeforeEach
    void setUp() {
        testInspectionTask = new InspectionTask();
        testInspectionTask.setId("test-id");
        testInspectionTask.setName("Test InspectionTask");
    }

    @Test
    void testCreate() throws Exception {
        when(inspectiontaskService.save(any(InspectionTask.class))).thenReturn(true);

        mockMvc.perform(post("/equipment/inspection")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testInspectionTask)))
                .andExpect(status().isOk())
                .andExpect(content().string("true"));
    }

    @Test
    void testGetById() throws Exception {
        when(inspectiontaskService.getById("test-id")).thenReturn(testInspectionTask);

        mockMvc.perform(get("/equipment/inspection/test-id"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value("test-id"))
                .andExpect(jsonPath("$.name").value("Test InspectionTask"));
    }
}