package org.simple.base.mapper;

import com.alibaba.fastjson2.JSONObject;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface FrCodeQuickMapper {

    @Insert("<script>" +
            "insert into ${table} (" +
            "<foreach item=\"field\" collection=\"fields\" separator=\",\">" +
            "${field}" +
            "</foreach>" +
            ") values (" +
            "<foreach item=\"val\" collection=\"value\" separator=\",\">" +
            "#{val}" +
            "</foreach>" +
            ")" +
            "</script>")
    int insertFormData(@Param("fields") List<String> fields,
                       @Param("value") List<Object> value,
                       @Param("table") String tableName);

    @Update("update ${table} set flow_id = #{value} where ${primaryKey} = #{id}")
    int updateFlowId(@Param("table") String table,
                     @Param("primaryKey") String primaryKey,
                     @Param("id") String id,
                     @Param("value") String value);

    @Select("select count(1) from information_schema.`TABLES` " +
            " where table_name = #{tableName}")
    int isHaveTable(@Param("tableName") String tableName);

    @Select("<script>" +
            "select " +
            "${field}" +
            " from  ${table}  where ${primaryKey} = #{id}" +
            "</script>")
    Object selectFormData(@Param("field") String field,
                          @Param("table") String tableName,
                          @Param("primaryKey") String primaryKey,
                          @Param("id") String id);

    @Update("update ${table} set ${field} = #{value} where ${primaryKey} = #{primary}")
    int updateFieldValue(@Param("table") String table,
                         @Param("primaryKey") String primaryKey,
                         @Param("primary") String primary,
                         @Param("field") String field,
                         @Param("value") Object value);

    @Delete("delete from ${tableName} where ${primaryKey} = #{id}")
    void delTableData(@Param("tableName") String tableName, @Param("primaryKey") String primaryKey,
                      @Param("id") String id);

    @Select("<script>" +
            "select " +
            "*" +
            " from  ${table}  where ${primaryKey} = #{id}" +
            "</script>")
    List<JSONObject> selectTableData(@Param("table") String tableName,
                                     @Param("primaryKey") String primaryKey,
                                     @Param("id") String id);

    @Select("select count(1) from information_schema.`COLUMNS` " +
            " where column_name = #{column} and table_name = #{table}")
    int isFieldExist(@Param("table") String table, @Param("column") String column);

    @Select("select count(1) from  ${table} " +
            " where ${primaryKey} = #{primaryKeyVal}")
    int isHaveData(@Param("table") String table, @Param("primaryKey") String primaryKey,
                   @Param("primaryKeyVal") String primaryKeyVal);
}
