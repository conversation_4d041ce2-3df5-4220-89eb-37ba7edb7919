package org.simple.base.filter;

import cn.hutool.core.util.StrUtil;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.simple.base.constant.AuthConstant;
import org.simple.base.wrapper.AuthRequestWrapper;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;


@Slf4j
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class SaServletFilter implements Filter {


    @Override
    public void doFilter(ServletRequest servletRequest,
                         ServletResponse servletResponse,
                         FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest request =
                (HttpServletRequest) servletRequest;
        // 创建一个包装器，以便添加或修改header
        AuthRequestWrapper wrappedRequest = new AuthRequestWrapper(request);
        String accessToken = request.getHeader(AuthConstant.TOKEN_NAME);
        if (StrUtil.isNotEmpty(accessToken)) {
            accessToken = accessToken.replace(AuthConstant.TOKEN_PIX, "");
        }
        wrappedRequest.addHeader(AuthConstant.OAUTH2_TOKEN_NAME, accessToken);
        filterChain.doFilter(wrappedRequest, servletResponse);

    }
}
