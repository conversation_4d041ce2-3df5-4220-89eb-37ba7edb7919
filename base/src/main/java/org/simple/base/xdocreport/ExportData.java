package org.simple.base.xdocreport;

import fr.opensagres.xdocreport.core.XDocReportException;
import fr.opensagres.xdocreport.document.IXDocReport;
import fr.opensagres.xdocreport.template.IContext;
import fr.opensagres.xdocreport.template.formatter.FieldsMetadata;

import java.io.IOException;
import java.io.OutputStream;

public class ExportData {

    private IXDocReport report;
    private IContext context;

    public ExportData(IXDocReport report, IContext context) {
        this.report = report;
        this.context = context;
    }

    public void process(OutputStream out) throws IOException, XDocReportException {
        report.process(context, out);
    }


    public void setData(String key, Object value) {
        FieldsMetadata fieldsMetadata = report.getFieldsMetadata();
        fieldsMetadata = fieldsMetadata == null ? new FieldsMetadata() : fieldsMetadata;
        fieldsMetadata.addFieldAsList(key);
        context.put(key, value);
    }
}
