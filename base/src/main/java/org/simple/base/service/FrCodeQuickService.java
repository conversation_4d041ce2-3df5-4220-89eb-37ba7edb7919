package org.simple.base.service;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.simple.base.mapper.FrCodeQuickMapper;
import org.simple.base.util.AuthUtil;
import org.simple.base.util.FormModelUtil;
import org.simple.base.vo.model.FieldModelVo;
import org.simple.base.vo.model.FormModelVo;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Slf4j
public class FrCodeQuickService {

    private final String createTime = "create_time";
    private final String updateTime = "update_time";
    private final String creator = "creator";

    @Resource
    private FrCodeQuickMapper frCodeQuickMapper;

    @DS("#dbName")
    public void dealInsertForm(String dbName, String formId, String privateKeyVal, JSONObject fieldValue) {
        //先保存表单数据，在发起流程
        FormModelVo formModelVo = FormModelUtil.getFormModel(formId);
        //判断表是否存在
        int count =
                frCodeQuickMapper.isHaveTable(formModelVo.getTableName());
        if (count == 0) {
            throw new RuntimeException(formModelVo.getTableName() + "表不存在，请先创建!");
        }
        List<FieldModelVo> fieldList = formModelVo.getFieldList();

        String tableName = formModelVo.getTableName();
        String primaryKey = formModelVo.getPrimaryKey();
        List<String> fields = new ArrayList<>();
        List<Object> values = new ArrayList<>();
        for (FieldModelVo o : fieldList) {
            if (!o.getGroup().equals("group") && !o.getType().equals("data_01")) {
                fields.add(o.getFieldNo());
                values.add(fieldValue.get(o.getId()));
            }
            //子表数据处理
            String isEdit = o.getConfig().getString("isEdit");
            if (o.getType().equals("data_01") && isEdit.equals("true")) {
                //先删除子表所有数据在做插入
                frCodeQuickMapper.delTableData(o.getTableName(),
                        o.getFieldNo(), privateKeyVal);
                //先获取所有的字段
                List<String> fields1 = new ArrayList<>();
                fields1.add(o.getFieldNo());
                JSONArray colArray = o.getConfig().getJSONArray("tableCol");
                for (int i = 0; i < colArray.size(); i++) {
                    fields1.add(colArray.getJSONObject(i).getString("key"));
                }
                //处理子表数据
                JSONArray jsonArray = fieldValue.getJSONArray(o.getId());
                for (int i = 0; i < jsonArray.size(); i++) {
                    List<Object> values1 = new ArrayList<>();
                    values1.add(privateKeyVal);
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    for (String k : fields1) {
                        if (!k.equals(o.getFieldNo())) {
                            values1.add(jsonObject.get(k));
                        }
                    }
                    //处理系统必要字段
                    dealSysField("add", o.getTableName(), fields1, values1, null, null);
                    frCodeQuickMapper.insertFormData(fields1, values1, o.getTableName());
                }
            }
        }
        if(!fields.contains(primaryKey)){
            fields.add(primaryKey);
            values.add(privateKeyVal);
        }
        //处理系统必要字段
        dealSysField("add", tableName, fields, values, null, null);
        frCodeQuickMapper.insertFormData(fields, values, tableName);
    }

    @DS("#dbName")
    public void dealFlowAfter(String dbName, String formId, String privateKeyVal, String processInstanceId) {
        //先保存表单数据，在发起流程
        FormModelVo formModelVo = FormModelUtil.getFormModel(formId);
        //流程发起成功，更新业务表的实例编号
        frCodeQuickMapper.updateFlowId(formModelVo.getTableName(),
                formModelVo.getPrimaryKey(), privateKeyVal,
                processInstanceId);
    }

    @DS("#dbName")
    public boolean isHaveData(String dbName, String formId, String privateKeyVal) {
        //先保存表单数据，在发起流程
        FormModelVo formModelVo = FormModelUtil.getFormModel(formId);
        //流程发起成功，更新业务表的实例编号
        return frCodeQuickMapper.isHaveData(formModelVo.getTableName(),
                formModelVo.getPrimaryKey(), privateKeyVal) > 0;
    }

    @DS("#dbName")
    public void dealEditForm(String dbName, String formId, String privateKeyVal, JSONObject formData) {
        //先保存表单数据，在发起流程
        FormModelVo formModelVo = FormModelUtil.getFormModel(formId);
        List<FieldModelVo> fieldList = formModelVo.getFieldList();
        for (FieldModelVo formField1 : fieldList) {
            if (!formField1.getGroup().equals("group") && !formField1.getType().equals("data_01")) {
                JSONObject obj = formField1.getConfig();
                String isEdit = obj.getString("isEdit");
                if (isEdit.equals("true")) {
                    Object fieldValue = formData.get(formField1.getId());
                    frCodeQuickMapper.updateFieldValue(
                            formModelVo.getTableName(), formModelVo.getPrimaryKey(),
                            privateKeyVal, formField1.getFieldNo(),
                            fieldValue
                    );
                }
            }
            //处理系统必要字段
            dealSysField("edit", formModelVo.getTableName(), null, null,
                    formModelVo.getPrimaryKey(), privateKeyVal);
            //子表数据处理
            String isEdit = formField1.getConfig().getString("isEdit");
            if (formField1.getType().equals("data_01") && isEdit.equals("true")) {
                //先删除子表所有数据在做插入
                frCodeQuickMapper.delTableData(formField1.getTableName(),
                        formField1.getFieldNo(), privateKeyVal);
                //先获取所有的字段
                List<String> fields1 = new ArrayList<>();
                fields1.add(formField1.getFieldNo());
                JSONArray colArray = formField1.getConfig().getJSONArray("tableCol");
                for (int i = 0; i < colArray.size(); i++) {
                    fields1.add(colArray.getJSONObject(i).getString("key"));
                }
                //处理子表数据
                JSONArray jsonArray = formData.getJSONArray(formField1.getId());
                for (int i = 0; i < jsonArray.size(); i++) {
                    List<Object> values1 = new ArrayList<>();
                    values1.add(privateKeyVal);
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    for (String k : fields1) {
                        if (!k.equals(formField1.getFieldNo())) {
                            values1.add(jsonObject.get(k));
                        }
                    }
                    //处理系统必要字段
                    dealSysField("add", formField1.getTableName(),
                            fields1, values1, null, null);
                    frCodeQuickMapper.insertFormData(fields1, values1, formField1.getTableName());
                }
            }
        }
    }

    @DS("#dbName")
    public List<Map<String, Object>> getFormData(String dbName, String formId, String privateKeyVal) {
        FormModelVo formModelVo = FormModelUtil.getFormModel(formId);
        String tableName = formModelVo.getTableName();
        String primaryKey = formModelVo.getPrimaryKey();
        List<FieldModelVo> list = formModelVo.getFieldList();
        List<Map<String, Object>> result = new ArrayList<>();
        for (FieldModelVo o : list) {
            if (!o.getGroup().equals("group") && !o.getType().equals("data_01")) {
                Map<String, Object> map = new HashMap<>();
                map.put("key", o.getId());
                if (o.getFieldNo().equalsIgnoreCase(primaryKey)) {
                    map.put("label", privateKeyVal);
                } else {
                    map.put("label", frCodeQuickMapper.selectFormData(o.getFieldNo(),
                            tableName, primaryKey, privateKeyVal));
                }
                result.add(map);
            }
            if (o.getType().equals("data_01")) {
                Map<String, Object> map = new HashMap<>();
                map.put("key", o.getId());
                map.put("label", frCodeQuickMapper.selectTableData(
                        o.getTableName(), o.getFieldNo(), privateKeyVal));
                result.add(map);
            }
        }
        return result;
    }

    @DS("#dbName")
    public void dealDelForm(String dbName, JSONArray tableList, String privateKeyVal) {
        for (int i = 0; i < tableList.size(); i++) {
            JSONObject obj1 = tableList.getJSONObject(i);
            String tableName = obj1.getString("tableName");
            String primaryKey = obj1.getString("tableKey");
            frCodeQuickMapper.delTableData(tableName, primaryKey, privateKeyVal);
        }
    }

    /**
     * 处理系统必要字段，create_time,update_time,creator
     */
    private void dealSysField(String type, String tableName, List<String> fields, List<Object> values,
                              String primaryKey, String privateKeyVal) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String currentDate = sdf.format(new Date());
        if (type.equals("add")) {
            if (frCodeQuickMapper.isFieldExist(tableName, createTime) > 0) {
                fields.add(createTime);
                values.add(currentDate);
            }
            if (frCodeQuickMapper.isFieldExist(tableName, updateTime) > 0) {
                fields.add(updateTime);
                values.add(currentDate);
            }
            if (frCodeQuickMapper.isFieldExist(tableName, creator) > 0) {
                fields.add(creator);
                values.add(AuthUtil.getUserId());
            }
        } else if (type.equals("edit")) {
            if (frCodeQuickMapper.isFieldExist(tableName, updateTime) > 0) {
                frCodeQuickMapper.updateFieldValue(
                        tableName, primaryKey, privateKeyVal, updateTime, currentDate
                );
            }
        }
    }

}
