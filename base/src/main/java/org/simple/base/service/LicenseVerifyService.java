package org.simple.base.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.net.NetUtil;
import de.schlichtherle.license.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.simple.base.dto.license.LicenseExtParam;
import org.simple.base.dto.license.LicenseInMemoryCache;
import org.simple.base.dto.license.LicenseSimpleInfo;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Objects;
import java.util.prefs.Preferences;

@Slf4j
@Component
public class LicenseVerifyService {

    private static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(LicenseVerifyService.class);

    @Value("${license.alias}")
    private String pubAlias;
    @Value("${license.storePwd}")
    private String keyStorePwd;
    @Value("${license.subject}")
    private String subject;
    @Value("${license.path}")
    private String licDir;
    @Value("${license.storePath}")
    private String pubPath;

    private final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    /**
     * 初始化授权文件
     */
    public void install() {
        try {
            LicenseManager licenseManager = LicenseManagerHolder.getLicenseManager(buildLicenseParam());
            LicenseInMemoryCache.setLicenseContent(
                    licenseManager.install(new File(Objects.requireNonNull(
                            getClass().getResource(licDir)).getPath()))
            );
            log.info("授权文件已初始化");
        } catch (LicenseContentException e) {
            log.error("授权文件已过期", e);
            Runtime.getRuntime().halt(1);
        } catch (Exception e) {
            log.error("授权文件初始化失败", e);
            Runtime.getRuntime().halt(1);
        }
    }

    /**
     * 构建授权文件的参数
     */
    private LicenseParam buildLicenseParam() {
        Class<LicenseVerifyService> clazz = LicenseVerifyService.class;
        Preferences pre = Preferences.userNodeForPackage(clazz);
        CipherParam cipherParam = new DefaultCipherParam(keyStorePwd);
        KeyStoreParam pubStoreParam = new DefaultKeyStoreParam(clazz,
                pubPath, pubAlias, keyStorePwd, null);
        return new DefaultLicenseParam(subject, pre, pubStoreParam, cipherParam);
    }

    /**
     * 验证授权文件的合法性
     */
    public boolean verify() {
        try {
            LicenseManager licenseManager = LicenseManagerHolder.getLicenseManager(buildLicenseParam());
            LicenseContent verify = licenseManager.verify();
            LicenseExtParam extra = (LicenseExtParam) verify.getExtra();
            String ip = extra.getIpAddress();
            String localIp = NetUtil.getLocalhostStr();
            List<String> ipAddress = CollUtil.newArrayList(ip.split(","));
            if (StringUtils.isNotEmpty(ip) && !ipAddress.contains(localIp)) {
                log.error("Ip地址验证不通过");
                return false;
            }
            String mac = extra.getMacAddress();
            String localMac = NetUtil.getLocalMacAddress();
            List<String> macAddress = CollUtil.newArrayList(mac.split(","));
            if (StringUtils.isNotEmpty(mac) && !macAddress.contains(localMac)) {
                log.error("网卡地址验证不通过");
                return false;
            }
            return true;
        } catch (LicenseContentException e) {
            log.error("授权文件已经过期", e);
            return false;
        } catch (Exception e) {
            log.error("授权文件验证失败", e);
            return false;
        }
    }

    /**
     * 获取授权信息
     */
    public LicenseSimpleInfo getLicenseInfo() {
        LicenseContent licenseContent = LicenseInMemoryCache.getLicenseContent();
        return LicenseSimpleInfo.builder()
                .licenseExtParam((LicenseExtParam) licenseContent.getExtra())
                .startDateTime(sdf.format(licenseContent.getNotBefore()))
                .endDateTime(sdf.format(licenseContent.getNotAfter()))
                .subject(licenseContent.getSubject())
                .info(licenseContent.getInfo())
                .exp(System.currentTimeMillis() > licenseContent.getNotAfter().getTime())
                .build();
    }

    /**
     * 更换授权文件
     */
    public LicenseSimpleInfo updateLicenseInfo(MultipartFile multipartFile)
            throws Exception {
        LicenseManager licenseManager = LicenseManagerHolder.getLicenseManager(buildLicenseParam());
        String licenseFullPath = Objects.requireNonNull(
                getClass().getResource(licDir)).getPath();
        File oldFile = new File(licenseFullPath);
        String licenseName = oldFile.getName();
        String licensePath = oldFile.getParentFile().getPath();
        String newLicenseName = System.currentTimeMillis() + licenseName;
        File newFile = new File(licensePath + "/" + newLicenseName);
        multipartFile.transferTo(newFile);
        try {
            LicenseContent licenseContent = licenseManager.install(newFile);
            if(!verify()){
                licenseManager.install(oldFile);
                throw new Exception("授权文件校验失败!");
            }
            LicenseInMemoryCache.setLicenseContent(
                    licenseContent
            );
        } catch (Exception ex) {
            newFile.delete();
            throw new RuntimeException(ex);
        }
        oldFile.delete();
        newFile.renameTo(oldFile);
        newFile.delete();
        LicenseContent licenseContent = LicenseInMemoryCache.getLicenseContent();
        return LicenseSimpleInfo.builder()
                .licenseExtParam((LicenseExtParam) licenseContent.getExtra())
                .startDateTime(sdf.format(licenseContent.getNotBefore()))
                .endDateTime(sdf.format(licenseContent.getNotAfter()))
                .subject(licenseContent.getSubject())
                .info(licenseContent.getInfo())
                .exp(System.currentTimeMillis() > licenseContent.getNotAfter().getTime())
                .build();
    }

}