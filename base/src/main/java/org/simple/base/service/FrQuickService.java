package org.simple.base.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.simple.base.mapper.FrQuickMapper;
import org.simple.base.util.DictUtil;
import org.simple.base.util.PageUtil;
import org.simple.base.vo.DictVo;
import org.simple.base.vo.model.DataModelVo;
import org.simple.base.vo.model.ModelParam;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class FrQuickService {

    private static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(FrQuickService.class);

    @Resource
    private FrQuickMapper frQuickMapper;

    @Resource
    private DataSource dataSource;

    @DS("#dbName")
    public Page<JSONObject> pageList(String dbName, DataModelVo dataModelVo, Page<JSONObject> page, ModelParam param) {
        JSONObject queryParam = createQueryParam(dataModelVo, param);
        Page<JSONObject> queryResultPage = frQuickMapper.pageList(page, queryParam);
        List<JSONObject> newList = new ArrayList<>();
        if (!queryResultPage.getRecords().isEmpty()) {
            for (JSONObject jsonObject : queryResultPage.getRecords()) {
                JSONObject resultObj = dealResult(dataModelVo, jsonObject);
                newList.add(resultObj);
            }
        }
        Page<JSONObject> result = PageUtil.copyNewPage(queryResultPage);
        result.setRecords(newList);
        return result;
    }

    @DS("#dbName")
    public List<JSONObject> list(String dbName, DataModelVo dataModelVo, ModelParam param) {
        JSONObject queryParam = createQueryParam(dataModelVo, param);
        List<JSONObject> queryResultList = frQuickMapper.list(queryParam);
        List<JSONObject> result = new ArrayList<>();
        if (!queryResultList.isEmpty()) {
            for (JSONObject jsonObject : queryResultList) {
                JSONObject resultObj = dealResult(dataModelVo, jsonObject);
                result.add(resultObj);
            }
        }
        return result;
    }

    @DS("#dbName")
    public JSONObject getOne(String dbName, DataModelVo dataModelVo, ModelParam param) {
        JSONObject queryParam = createQueryParam(dataModelVo, param);
        JSONObject queryResult =
                frQuickMapper.getOne(queryParam);
        return dealResult(dataModelVo, queryResult);
    }


    /**
     * 组装请求参数
     */
    private JSONObject createQueryParam(DataModelVo dataModelVo, ModelParam param) {
        JSONObject queryParam = new JSONObject();
        queryParam.put("mainSql", dataModelVo.getSqlText());
        //组装请求参数
        List<JSONObject> paramList = new ArrayList<>();
        JSONArray req = dataModelVo.getReqData();
        for (int i = 0; i < req.size(); i++) {
            JSONObject reqObj = req.getJSONObject(i);
            JSONObject extAttr = reqObj.getJSONObject("extAttr");
            String attrNo = reqObj.getString("attrNo");
            if (!StrUtil.isEmptyIfStr(param.get(attrNo))) {
                JSONObject paramObj = new JSONObject();
                paramObj.put("value", param.get(attrNo));
                paramObj.put("field", extAttr.getString("field"));
                paramObj.put("type", extAttr.getString("type"));
                paramObj.put("con", extAttr.getString("con"));
                paramList.add(paramObj);
            }
        }
        //判断是否有排序字段
        if (StrUtil.isNotEmpty(dataModelVo.getSort())) {
            queryParam.put("sort", dataModelVo.getSort());
        } else {
            queryParam.put("sort", "");
        }
        queryParam.put("param", paramList);
        return queryParam;
    }


    /**
     * 处理每一行返数据
     */
    private JSONObject dealResult(DataModelVo dataModelVo, JSONObject queryResult) {
        JSONObject result = new JSONObject();
        if (null == queryResult) {
            return result;
        }
        JSONArray outData = dataModelVo.getOutData();
        //开始循环过滤返回数据
        for (int i = 0; i < outData.size(); i++) {
            JSONObject outObj = outData.getJSONObject(i);
            JSONObject extAttr = outObj.getJSONObject("extAttr");
            String attrNo = outObj.getString("attrNo");
            String fieldNo = outObj.getString("fieldNo");
            if (null != queryResult.get(fieldNo)) {
                if (extAttr.getString("type").equals("text")) {
                    result.put(attrNo, queryResult.get(fieldNo));
                } else if (extAttr.getString("type").equals("date")) {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    Date date = (Date) queryResult.get(fieldNo);
                    result.put(attrNo, sdf.format(date));
                } else if (extAttr.getString("type").equals("datetime")) {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    Date date = (Date) queryResult.get(fieldNo);
                    result.put(attrNo, sdf.format(date));
                } else if (extAttr.getString("type").equals("select")) {
                    String value = queryResult.getString(fieldNo);
                    DictVo d = DictUtil.getDictVoByValue(extAttr.getString("dict"), value);
                    result.put(attrNo, value);
                    result.put("dict_" + attrNo, d.getLabel());
                }
            }
        }
        return result;
    }

    @DS("master")
    public byte[] createSql(String tableName, String primaryKey, String keyValues) {
        byte[] result = null;
        DynamicRoutingDataSource ds = (DynamicRoutingDataSource) dataSource;
        DataSource data = ds.getDataSource("master");
        try {
            String sql =
                    getUpgradeSql(data.getConnection(), tableName, primaryKey, keyValues);
            result = sql.getBytes();
        } catch (Exception ex) {
            log.error("生成失败：", ex);
            throw new RuntimeException(ex);
        }
        return result;
    }

    // 先delete，然后insert
    private static String getUpgradeSql(Connection conn, String tableNames,
                                        String primaryKeys, String keyValues)
            throws SQLException {
        StringBuilder insertDataSQL = new StringBuilder();
        StringBuilder insert_into_ = new StringBuilder();
        String[] tables = tableNames.split(",");
        String[] keys = primaryKeys.split(",");
        for (int j = 0; j < tables.length; j++) {
            String primaryKey = keys[j];
            String tableName = tables[j];
            String[] vals = keyValues.split(",");
            StringBuilder primaryCon = new StringBuilder();
            for (String str : vals) {
                primaryCon.append("'").append(str).append("',");
            }
            StringBuilder exeSql = new StringBuilder();
            exeSql.append("select * from ").append(tableName).append(" where ").append(primaryKey)
                    .append(" in (").append(primaryCon, 0,
                            primaryCon.toString().length() - 1).append(") ");
            Statement stmt = conn.createStatement();
            ResultSet rs = stmt.executeQuery(exeSql.toString());
            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();

            insertDataSQL.append("delete from ").append(tableName).append(" where ").append(primaryKey)
                    .append(" in (").append(primaryCon, 0,
                            primaryCon.toString().length() - 1).append(") ;");
            insertDataSQL.append("\n");
            while (rs.next()) {
                StringBuilder values = new StringBuilder();
                StringBuilder colVal = new StringBuilder();
                for (int i = 1; i <= columnCount; i++) {
                    Object value = rs.getObject(i);
                    String colName = metaData.getColumnName(i);
                    if (value != null) {
                        values.append("'").append(value).append("',");
                    } else {
                        values.append("null,");
                    }
                    colVal.append(colName).append(",");
                }
                insert_into_ = insertDataSQL.append("INSERT INTO ").
                        append(tableName)
                        .append("(").append(colVal, 0, colVal.toString().length() - 1).append(")")
                        .append(" VALUES (").append(values, 0, values.toString().length() - 1).append(");");
                insertDataSQL.append("\n");
            }
            insertDataSQL.append("commit;");
            insertDataSQL.append("\n");
        }
        return insertDataSQL.toString();
    }
}
