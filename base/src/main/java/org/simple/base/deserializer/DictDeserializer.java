package org.simple.base.deserializer;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONArray;
import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import org.simple.base.vo.DictVo;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class DictDeserializer extends JsonDeserializer<List<DictVo>> {
    @Override
    public List<DictVo> deserialize(JsonParser jsonParser,
                                    DeserializationContext deserializationContext)
            throws IOException, JacksonException {
        List<DictVo> list = new ArrayList<>();
        JSONArray array =
                JSONArray.parseArray(jsonParser.getText());
        array.forEach(o -> {
            DictVo dictVo = new DictVo();
            BeanUtil.copyProperties(o, dictVo);
            list.add(dictVo);
        });
        return list;
    }
}
