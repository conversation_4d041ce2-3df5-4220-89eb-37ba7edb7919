package org.simple.base.storage;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.IoUtil;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.internal.OSSHeaders;
import com.aliyun.oss.model.*;
import org.apache.commons.lang.StringUtils;
import org.simple.base.constant.RedisConstant;
import org.simple.base.dto.FileDto;
import org.simple.base.dto.OssDto;
import org.simple.base.vo.FrResult;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class AliOss {
    private static AliOss aliOss = null;

    private static OssDto ossDto;

    private AliOss() {
    }

    public static AliOss getInstance(RedisTemplate<String, Object> template) {
        if (null == aliOss) {
            aliOss = new AliOss();
        }
        //设置配置对象
        ossDto = BeanUtil.fillBeanWithMap(
                template.opsForHash().entries(RedisConstant.ALIOSS_PIX), new OssDto(),
                false);
        return aliOss;
    }

    private OSS getOssClient() {
        return new OSSClientBuilder().build(ossDto.getEndpoint(),
                ossDto.getAccessKeyId(), ossDto.getAccessKeySecret());
    }

    public FrResult<String> fileUpload(MultipartFile file, String fileName, boolean isPrivate) {
        //初始化ossclient对象
        OSS ossClient = getOssClient();
        //String fileName = file.getOriginalFilename();
        String path = getFilePath();
        // 创建PutObjectRequest对象。
        try {
            PutObjectRequest putObjectRequest = new PutObjectRequest(
                    ossDto.getWorkspace(), path+"/"+fileName, file.getInputStream());
            // 如果需要上传时设置存储类型与访问权限
            if (isPrivate) {
                ObjectMetadata metadata = new ObjectMetadata();
                metadata.setHeader(OSSHeaders.OSS_STORAGE_CLASS, StorageClass.Standard.toString());
                metadata.setObjectAcl(CannedAccessControlList.Private);
                putObjectRequest.setMetadata(metadata);
            }
            ossClient.putObject(putObjectRequest);
            //若是私有连接则返回上传路径，若是公共读则返回请求的url地址
            return FrResult.success(path);
        } catch (Exception ex) {
            return FrResult.failed("上传失败：" + ex.getMessage());
        } finally {
            //最后关闭ossclient
            ossClient.shutdown();
        }
    }

    /**
     * 获取私有文件授权链接
     *
     * @param filepath : 路径
     */
//    public String downLoadLink(String filepath, Long expir) {
//        OSS ossClient = getOssClient();
//        Date expiration = new Date(new Date().getTime() + expir * 1000);
//        URL url = ossClient.generatePresignedUrl(ossDto.getWorkspace(), filepath, expiration);
//        ossClient.shutdown();
//        if (!url.toString().contains("https")) {
//            return url.toString().replace("http", "https");
//        } else {
//            return url.toString();
//        }
//    }


    /**
     * 下载文件，返回输入流
     *
     * @param filepath ： 路径
     */
    public void downLoad(String fileName,String filepath, OutputStream out) throws IOException {
        OSS ossClient = getOssClient();
        OSSObject ossObject = ossClient.getObject(ossDto.getWorkspace(), filepath+"/"+fileName);
        IoUtil.copy(ossObject.getObjectContent(),out);
        ossObject.close();
        ossClient.shutdown();
    }

    /**
     * 删除文件
     *
     * @param filepath ： 路径
     */
    public boolean remove(String fileName,String filepath) throws IOException {
        OSS ossClient = getOssClient();
        VoidResult result =
                ossClient.deleteObject(ossDto.getWorkspace(), filepath+"/"+fileName);
         ossClient.shutdown();
         return true;
    }

    /**
     * 查询文件列表
     */
    public FileDto listFiles(Integer size, String marker, String prefix) {
        OSS ossClient = getOssClient();
        ListObjectsRequest request = new ListObjectsRequest(ossDto.getWorkspace());
        if (StringUtils.isNotEmpty(marker)) {
            request.setMarker(marker);
        }
        request.setPrefix(prefix);
        request.setMaxKeys(size);
        ObjectListing objectListing = ossClient.listObjects(request);
        List<OSSObjectSummary> list = objectListing.getObjectSummaries();
        List<org.simple.base.dto.File> listfile = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(row -> {
                org.simple.base.dto.File f = new org.simple.base.dto.File();
                f.setKey(row.getKey());
                f.setSize(row.getSize());
                f.setUpdateDate(row.getLastModified());
                listfile.add(f);
            });
        }
        FileDto fIleDto = new FileDto();
        fIleDto.setFileList(listfile);
        fIleDto.setNextMarker(objectListing.getNextMarker());
        return fIleDto;
    }

    /**
     * 凭借文件存储路径
     */
    private String getFilePath() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat sdf1 = new SimpleDateFormat("HHmm");
        Date date = new Date();
        return sdf.format(date) + "/" + sdf1.format(date);
    }
}
