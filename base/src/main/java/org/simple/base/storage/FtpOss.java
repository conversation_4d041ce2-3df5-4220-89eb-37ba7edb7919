package org.simple.base.storage;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.extra.ftp.Ftp;
import cn.hutool.extra.ftp.FtpConfig;
import cn.hutool.extra.ftp.FtpMode;
import org.simple.base.constant.RedisConstant;
import org.simple.base.dto.OssDto;
import org.simple.base.vo.FrResult;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;

public class FtpOss {

    private static FtpOss ftpOss = null;

    private static OssDto ossDto;

    private FtpOss() {
    }

    public static FtpOss getInstance(RedisTemplate<String, Object> template) {
        if (null == ftpOss) {
            ftpOss = new FtpOss();
        }
        //设置配置对象
        ossDto = BeanUtil.fillBeanWithMap(
                template.opsForHash().entries(RedisConstant.FTPOSS_PIX), new OssDto(),
                false);
        return ftpOss;
    }

    private Ftp getFtp() {
        String host = ossDto.getEndpoint();
        int port = Integer.parseInt(ossDto.getRegion());
        String pwd = ossDto.getAccessKeySecret();
        String user = ossDto.getAccessKeyId();
        FtpConfig ftpConfig = new FtpConfig();
        ftpConfig.setCharset(StandardCharsets.UTF_8);
        ftpConfig.setHost(host);
        ftpConfig.setPort(port);
        ftpConfig.setUser(user);
        ftpConfig.setPassword(pwd);
        return new Ftp(ftpConfig, FtpMode.Passive);
    }

    /**
     * ftp删除附件
     */
    public boolean remove(String fileName, String filePath)
            throws IOException {
        String path = ossDto.getWorkspace();
        try (Ftp ftp = getFtp()) {
            ftp.cd(path + filePath);
            return ftp.delFile(fileName);
        } catch (Exception e) {
            throw new IOException(e);
        }
    }

    /**
     * ftp下载
     */
    public void downLoad(String fileName, String filePath
            , OutputStream out)
            throws IOException {
        String path = ossDto.getWorkspace();
        try (Ftp ftp = getFtp()) {
            ftp.cd(path + filePath);
            ftp.download(null, fileName, out);
        } catch (Exception e) {
            throw new IOException(e);
        }
    }

    /**
     * ftp上传
     */
    public FrResult<String> fileUpload(MultipartFile file, String fileName) throws IOException {
        String path = ossDto.getWorkspace();
        String filePath = "";
        try (Ftp ftp = getFtp()) {
            ftp.cd(path);
            filePath = getFilePath();
            boolean isExits = ftp.cd(filePath);
            if (!isExits) {
                ftp.mkDirs(filePath);
                ftp.cd(filePath);
            }
            ftp.upload(null, fileName, file.getInputStream());
        } catch (Exception e) {
            return FrResult.failed(e.getMessage());
        }
        return FrResult.success(filePath);
    }

    /**
     * 凭借文件存储路径
     */
    private String getFilePath() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat sdf1 = new SimpleDateFormat("HHmm");
        Date date = new Date();
        return "/" + sdf.format(date) + "/" + sdf1.format(date);
    }
}
