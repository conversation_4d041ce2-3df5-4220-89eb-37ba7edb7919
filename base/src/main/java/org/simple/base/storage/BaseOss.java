package org.simple.base.storage;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.FileUtil;
import org.simple.base.constant.RedisConstant;
import org.simple.base.dto.OssDto;
import org.simple.base.vo.FrResult;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.Date;

public class BaseOss {

    private static BaseOss baseOss = null;

    private static OssDto ossDto;

    private BaseOss() {
    }

    public static BaseOss getInstance(RedisTemplate<String, Object> template) {
        if (null == baseOss) {
            baseOss = new BaseOss();
        }
        //设置配置对象
        ossDto = BeanUtil.fillBeanWithMap(
                template.opsForHash().entries(RedisConstant.BASEOSS_PIX), new OssDto(),
                false);
        return baseOss;
    }

    /**
     * 删除文件
     */
    public boolean remove(String fileName, String filePath) {
        String path = ossDto.getWorkspace(); //本地上传地址
        String fileFullPath = path + filePath + "/" + fileName;
        File file = new File(fileFullPath);
        return file.delete();
    }


    /**
     * 本地上传
     */
    public FrResult<String> fileUpload(MultipartFile file, String fileName) throws IOException {
        String path = ossDto.getWorkspace(); //本地上传地址
        String filePath = getFilePath();
        File filePathFile = new File(path + filePath);
        if (!filePathFile.exists()) {
            filePathFile.mkdirs();
        }
        File file1 = new File(path + filePath + "/" + fileName);
        if (file1.createNewFile()) {
            FileUtil.writeFromStream(file.getInputStream(), file1);
        } else {
            return FrResult.failed("文件创建失败");
         }
        return FrResult.success(filePath);
    }



    /**
     * 本地下载
     */
    public void downLoad(String fileName, String filePath
            , OutputStream out) {
        String path = ossDto.getWorkspace(); //本地上传地址
        String fileFullPath = path + filePath + "/" + fileName;
        File file = new File(fileFullPath);
        FileUtil.writeToStream(file, out);
    }


    /**
     * 凭借文件存储路径
     */
    private String getFilePath() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat sdf1 = new SimpleDateFormat("HHmm");
        Date date = new Date();
        return "/" + sdf.format(date) + "/" + sdf1.format(date);
    }
}
