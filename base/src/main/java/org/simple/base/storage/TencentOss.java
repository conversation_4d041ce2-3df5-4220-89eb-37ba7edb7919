package org.simple.base.storage;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.IoUtil;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.exception.CosClientException;
import com.qcloud.cos.http.HttpProtocol;
import com.qcloud.cos.model.*;
import com.qcloud.cos.region.Region;
import org.simple.base.constant.RedisConstant;
import org.simple.base.dto.FileDto;
import org.simple.base.dto.OssDto;
import org.simple.base.vo.FrResult;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TencentOss {

    private static TencentOss tencentOss = null;
    private RedisTemplate<String, Object> redisTemplate;

    private static OssDto ossDto;

    private TencentOss() {
    }

    public static TencentOss getInstance(RedisTemplate<String, Object> template) {
        if (null == tencentOss) {
            tencentOss = new TencentOss();
        }
        //设置配置对象
        ossDto = BeanUtil.fillBeanWithMap(
                template.opsForHash().entries(RedisConstant.TENCENT_PIX), new OssDto(),
                false);
        return tencentOss;
    }

    private COSClient getCosClient() {
        COSCredentials cred = new BasicCOSCredentials(ossDto.getAccessKeyId(),
                ossDto.getAccessKeySecret());
        Region region = new Region(ossDto.getRegion());
        ClientConfig clientConfig = new ClientConfig(region);
        clientConfig.setHttpProtocol(HttpProtocol.https);
        return new COSClient(cred, clientConfig);
    }

    public FrResult<String> fileUpload(MultipartFile file, String fileName, boolean isPrivate) {
        //初始化ossclient对象
        COSClient cosClient = getCosClient();
        //String fileName = file.getOriginalFilename();
        String path = getFilePath();
        // 创建PutObjectRequest对象。
        try {
            PutObjectRequest putObjectRequest = new PutObjectRequest(ossDto.getWorkspace(),
                    path + "/" + fileName, file.getInputStream(), null);
            // 如果需要上传时设置存储类型与访问权限
            if (isPrivate) {
                putObjectRequest.setCannedAcl(CannedAccessControlList.Private);
            }
            cosClient.putObject(putObjectRequest);
            //若是私有连接则返回上传路径，若是公共读则返回请求的url地址
            return FrResult.success(path);
        } catch (Exception ex) {
            return FrResult.failed("上传失败：" + ex.getMessage());
        } finally {
            //最后关闭ossclient
            cosClient.shutdown();
        }
    }

    /**
     * 获取私有文件授权链接
     *
     * @param filepath:路径
     */
//    public String downLoadLink(String filepath, Long expir) {
//        COSClient cosClient = getCosClient();
//        Date expiration = new Date(new Date().getTime() + expir * 1000);
//        URL url = cosClient.generatePresignedUrl(ossDto.getWorkspace(), filepath, expiration);
//        cosClient.shutdown();
//        if (!url.toString().contains("https")) {
//            return url.toString().replace("http", "https");
//        } else {
//            return url.toString();
//        }
//    }


    /**
     * 下载文件，返回输入流
     *
     * @param filepath:路径
     */
    public void downLoad(String fileName,String filepath, OutputStream out) throws IOException {
        COSClient cosClient = getCosClient();
        GetObjectRequest getObjectRequest = new GetObjectRequest(ossDto.getWorkspace(), filepath+"/"+fileName);
        COSObject cosObject = cosClient.getObject(getObjectRequest);
        COSObjectInputStream cosObjectInput = cosObject.getObjectContent();
        IoUtil.copy(cosObjectInput,out);
        cosObject.close();
        cosClient.shutdown();
    }

    /**
     * 删除文件
     *
     * @param filepath ： 路径
     */
    public boolean remove(String fileName,String filepath) throws IOException {
        COSClient cosClient = getCosClient();
        cosClient.deleteObject(ossDto.getWorkspace(), filepath+"/"+fileName);
        cosClient.shutdown();
        return true;
    }


    /**
     * 查询文件列表
     */
    public FileDto listFiles(Integer size, String marker, String prefix) {
        COSClient cosClient = getCosClient();
        ListObjectsRequest listObjectsRequest = new ListObjectsRequest();
        listObjectsRequest.setBucketName(ossDto.getWorkspace());
        listObjectsRequest.setPrefix(prefix);
        listObjectsRequest.setMaxKeys(size);
        ObjectListing objectListing = null;
        try {
            objectListing = cosClient.listObjects(listObjectsRequest);
        } catch (CosClientException e) {
            e.printStackTrace();
        }
        List<COSObjectSummary> list = objectListing.getObjectSummaries();
        List<org.simple.base.dto.File> listfile = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(row -> {
                org.simple.base.dto.File f = new org.simple.base.dto.File();
                f.setKey(row.getKey());
                f.setSize(row.getSize());
                f.setUpdateDate(row.getLastModified());
                listfile.add(f);
            });
        }
        FileDto fIleDto = new FileDto();
        fIleDto.setFileList(listfile);
        fIleDto.setNextMarker(objectListing.getNextMarker());
        return fIleDto;
    }

    /**
     * 凭借文件存储路径
     */
    private static String getFilePath() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat sdf1 = new SimpleDateFormat("HHmm");
        Date date = new Date();
        return sdf.format(date) + "/" + sdf1.format(date);
    }
}
