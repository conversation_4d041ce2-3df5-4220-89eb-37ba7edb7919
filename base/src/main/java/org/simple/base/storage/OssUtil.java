package org.simple.base.storage;


import cn.hutool.core.util.ObjUtil;
import io.minio.errors.*;
import org.simple.base.constant.RedisConstant;
import org.simple.base.vo.FrResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.OutputStream;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

/**
 * 文件存储工具类
 */
@Component
public class OssUtil {

    private static RedisTemplate<String, Object> redisTemplate;

    @Autowired
    public void setRedisTemplate(RedisTemplate<String, Object> redisTemplate) {
        OssUtil.redisTemplate = redisTemplate;
    }

    private static String isUse() {
        if (ObjUtil.isEmpty(redisTemplate.opsForValue().get(RedisConstant.USEOSS_PIX))) {
            throw new RuntimeException("未设置文件存储方式");
        }
        return String.valueOf(redisTemplate.opsForValue().get(RedisConstant.USEOSS_PIX));
    }

    public static FrResult<String> upLoad(MultipartFile multipartFile, String fileName, boolean isPri) throws ServerException,
            InsufficientDataException, ErrorResponseException, IOException, NoSuchAlgorithmException,
            InvalidKeyException, InvalidResponseException, XmlParserException, InternalException {
        String useOssKey = isUse();
        return switch (useOssKey) {
            case RedisConstant.ALIOSS_PIX -> AliOss.getInstance(redisTemplate).fileUpload(
                    multipartFile, fileName, isPri
            );
            case RedisConstant.TENCENT_PIX -> TencentOss.getInstance(redisTemplate).fileUpload(
                    multipartFile, fileName, isPri
            );
            case RedisConstant.MINIO_PIX -> MinioOss.getInstance(redisTemplate).fileUpload(
                    multipartFile, fileName, isPri
            );
            case RedisConstant.BASEOSS_PIX -> BaseOss.getInstance(redisTemplate).fileUpload(
                    multipartFile, fileName
            );
            case RedisConstant.FTPOSS_PIX -> FtpOss.getInstance(redisTemplate).fileUpload(
                    multipartFile, fileName
            );
            default -> throw new IllegalStateException("Unexpected value: " + useOssKey);
        };
    }

    public static boolean remove(String filePath, String fileName)
            throws ServerException, InsufficientDataException, ErrorResponseException, IOException,
            NoSuchAlgorithmException, InvalidKeyException, InvalidResponseException,
            XmlParserException, InternalException {
        String useOssKey = isUse();
        return switch (useOssKey) {
            case RedisConstant.ALIOSS_PIX -> AliOss.getInstance(redisTemplate).remove(
                    fileName, filePath
            );
            case RedisConstant.TENCENT_PIX -> TencentOss.getInstance(redisTemplate).remove(
                    fileName, filePath
            );
            case RedisConstant.MINIO_PIX -> MinioOss.getInstance(redisTemplate).remove(
                    fileName, filePath
            );
            case RedisConstant.BASEOSS_PIX -> BaseOss.getInstance(redisTemplate).remove(
                    fileName, filePath
            );
            case RedisConstant.FTPOSS_PIX -> FtpOss.getInstance(redisTemplate).remove(
                    fileName, filePath
            );
            default -> throw new IllegalStateException("Unexpected value: " + useOssKey);
        };
    }

    public static void downLoad(String filePath, String fileName
            , OutputStream out) throws IOException, ServerException,
            InsufficientDataException, ErrorResponseException,
            NoSuchAlgorithmException, InvalidKeyException,
            InvalidResponseException, XmlParserException,
            InternalException {
        String useOssKey = isUse();
        switch (useOssKey) {
            case RedisConstant.ALIOSS_PIX -> AliOss.getInstance(redisTemplate).downLoad(
                    fileName, filePath, out
            );
            case RedisConstant.TENCENT_PIX -> TencentOss.getInstance(redisTemplate).downLoad(
                    fileName, filePath, out
            );
            case RedisConstant.MINIO_PIX -> MinioOss.getInstance(redisTemplate).downLoad(
                    fileName, filePath, out
            );
            case RedisConstant.BASEOSS_PIX -> BaseOss.getInstance(redisTemplate).downLoad(
                    fileName, filePath, out
            );
            case RedisConstant.FTPOSS_PIX -> FtpOss.getInstance(redisTemplate).downLoad(
                    fileName, filePath, out
            );
            default -> throw new IllegalStateException("Unexpected value: " + useOssKey);
        }
    }
}
