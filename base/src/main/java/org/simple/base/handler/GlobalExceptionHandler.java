package org.simple.base.handler;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.exception.NotPermissionException;
import cn.dev33.satoken.oauth2.exception.SaOAuth2ClientModelException;
import cn.dev33.satoken.oauth2.exception.SaOAuth2RefreshTokenException;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.alibaba.csp.sentinel.slots.block.authority.AuthorityException;
import com.alibaba.csp.sentinel.slots.block.degrade.DegradeException;
import com.alibaba.csp.sentinel.slots.block.flow.FlowException;
import com.alibaba.csp.sentinel.slots.block.flow.param.ParamFlowException;
import com.alibaba.csp.sentinel.slots.system.SystemBlockException;
import lombok.extern.slf4j.Slf4j;
import org.simple.base.enums.ResultCodeEnum;
import org.simple.base.exception.CustomException;
import org.simple.base.exception.FileException;
import org.simple.base.exception.SecurityException;
import org.simple.base.exception.WorkFlowException;
import org.simple.base.vo.FrResult;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 统一异常处理
 *
 * <AUTHOR>
 * @version v1.0
 * @since 2022/7/11
 */
@RestControllerAdvice(basePackages = {"org.simple"})
@Slf4j
public class GlobalExceptionHandler {

    private static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(GlobalExceptionHandler.class);

    @ExceptionHandler(value = Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public FrResult<String> exceptionHandler(Exception ex) {
        log.error("未知异常：", ex);
        return FrResult.failed(ex.getMessage());
    }


    @ExceptionHandler(value = SaOAuth2ClientModelException.class)
    public FrResult<String> saOAuth2ExceptionHandler(SaOAuth2ClientModelException ex) {
        if (ex.getCode() == 30115) {
            return FrResult.failed("oauth2.0 授权认证失败：appSecret/clientSecret错误");
        }
        if (ex.getCode() == 30105) {
            return FrResult.failed("oauth2.0 授权认证失败：appId/clientId错误");
        }
        return FrResult.failed("oauth2.0 授权认证失败");
    }

    @ExceptionHandler(value = SaOAuth2RefreshTokenException.class)
    public FrResult<String> saOAuth2RefreshTokenException(SaOAuth2RefreshTokenException ex) {
        return FrResult.failed("oauth2.0 授权认证失败：refresh_token无效");
    }


    /**
     * 流程异常捕捉处理
     *
     * @param ex 异常信息
     * @return 统一结果
     */
    @ExceptionHandler(value = WorkFlowException.class)
    public FrResult<?> workFlowExceptionHandler(WorkFlowException ex) {
        return FrResult.failed(ex.getErrorMessage());
    }

    /**
     * 自定义异常捕捉处理
     *
     * @param ex 异常信息
     * @return 统一结果
     */
    @ExceptionHandler(value = CustomException.class)
    public FrResult<?> customExceptionHandler(CustomException ex) {
        return FrResult.failed(ex.getErrorMessage());
    }

    /**
     * 文件操作异常捕捉处理
     *
     * @param ex 异常信息
     * @return 统一结果
     */
    @ExceptionHandler(value = FileException.class)
    public FrResult<?> fileExceptionHandler(FileException ex) {
        return FrResult.failed(ex.getErrorMessage());
    }

    /**
     * xss,sql注入安全拦截
     *
     * @param ex 异常信息
     * @return 统一结果
     */
    @ExceptionHandler(value = SecurityException.class)
    @ResponseBody
    public ResponseEntity<FrResult<?>> securityExceptionHandler(SecurityException ex) {
        return new ResponseEntity<FrResult<?>>(
                FrResult.failed(ex.getErrorMessage()), HttpStatus.BAD_REQUEST);
    }


    /**
     * Sa-token未登录异常处理
     *
     * @param exception 异常
     * @return 返回
     */
    @ExceptionHandler(value = NotLoginException.class)
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    public FrResult<Boolean> unauthenticatedExceptionHandler(NotLoginException exception) {
        log.error("sa-token授权异常：", exception);
        // 判断场景值，定制化异常信息
        ResultCodeEnum resultCode;
        if (exception.getType().equals(NotLoginException.NOT_TOKEN)) {
            return FrResult.tokenTimeOut();
        } else if (exception.getType().equals(NotLoginException.INVALID_TOKEN)) {
            return FrResult.tokenTimeOut();
        } else if (exception.getType().equals(NotLoginException.TOKEN_TIMEOUT)) {
            return FrResult.tokenTimeOut();
        } else if (exception.getType().equals(NotLoginException.BE_REPLACED)) {
            return FrResult.tokenTimeOut();
        } else if (exception.getType().equals(NotLoginException.KICK_OUT)) {
            return FrResult.tokenTimeOut();
        } else {
            resultCode = ResultCodeEnum.NOT_TOKEN_EXCEPTION;
        }
        return FrResult.failed(resultCode.getMsg());
    }


    /**
     * Sa-token 接口无权返回返回信息增强
     *
     * @param exception 异常
     * @return 返回
     */
    @ExceptionHandler(value = NotPermissionException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public FrResult<?> handleNotPermissionException(NotPermissionException exception) {
        return FrResult.failed(ResultCodeEnum.FORBIDDEN.getMsg());
    }

    /**
     * 断言异常
     *
     * @param exception 异常
     * @return 返回
     */
    @ExceptionHandler(value = IllegalArgumentException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public FrResult<Boolean> illegalArgumentException(IllegalArgumentException exception) {
        return FrResult.failed(ResultCodeEnum.ILLEGAL_ARGUMENT_EXCEPTION.getMsg());
    }

    /**
     * sentinel 限流异常
     */
    @ExceptionHandler(BlockException.class)
    @ResponseStatus(HttpStatus.TOO_MANY_REQUESTS)
    public FrResult<?> sentinelBlockHandler(BlockException e) {
        log.error("sentinel限流异常：", e);
        if (e instanceof FlowException) {
            return FrResult.rateLimit("访问太快了");
        } else if (e instanceof DegradeException) {
            return FrResult.rateLimit("服务异常");
        } else if (e instanceof ParamFlowException) {
            return FrResult.rateLimit("访问太快了");
        } else if (e instanceof SystemBlockException) {
            return FrResult.rateLimit("系统异常");
        } else if (e instanceof AuthorityException) {
            return FrResult.rateLimit("授权规则异常");
        }
        return FrResult.rateLimit(e.getMessage());
    }
}
