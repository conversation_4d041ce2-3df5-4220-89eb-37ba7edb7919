package org.simple.base.handler;

import cn.dev33.satoken.util.SaResult;
import com.alibaba.fastjson2.JSONObject;
import de.schlichtherle.license.LicenseContent;
import org.simple.base.dto.UserDto;
import org.simple.base.dto.license.LicenseInMemoryCache;
import org.simple.base.util.AuthUtil;
import org.simple.base.util.SysConfigUtil;
import org.simple.base.vo.FrResult;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import javax.annotation.Nonnull;
import java.time.LocalDateTime;

/**
 * 统一请求返回
 *
 * <AUTHOR>
 * @version v1.0
 * @since 2022/7/9
 */
@RestControllerAdvice(basePackages = {"org.simple"})
public class GlobalResultHandler implements ResponseBodyAdvice<Object> {

    @Value("${license.enable}")
    private boolean licenseEnable;

    /**
     * 支持的范围
     *
     * @param methodParameter swagger方法
     * @param aClass          http消息转化类
     */
    @Override
    public boolean supports(MethodParameter methodParameter, @Nonnull Class<? extends HttpMessageConverter<?>> aClass) {
        /* 排除swagger自带的内容 */
        return !methodParameter.getDeclaringClass().getName().contains("springdoc");
    }

    /**
     * 跨域配置
     *
     * @param body 返回的内容体
     */
    @Override
    public Object beforeBodyWrite(Object body, @Nonnull MethodParameter methodParameter, @Nonnull MediaType mediaType, @Nonnull Class<? extends HttpMessageConverter<?>> aClass, @Nonnull ServerHttpRequest serverHttpRequest, @Nonnull ServerHttpResponse serverHttpResponse) {
        LicenseContent licenseContent = LicenseInMemoryCache.getLicenseContent();
        if(licenseEnable && System.currentTimeMillis() > licenseContent.getNotAfter().getTime()){
            /* 防止返回类型不是包装类型，但是抛出异常，被处理成包装类型 */
            if (body instanceof FrResult) {
                return FrResult.expLicense((FrResult)body) ;
            }
            if(body instanceof SaResult){
                return FrResult.success(body);
            }
            return JSONObject.toJSONString(FrResult.expLicense(FrResult.success(body, "操作成功")));
        }else{
            /* 防止返回类型不是包装类型，但是抛出异常，被处理成包装类型 */
            if (body instanceof FrResult) {
                //判断密码是否过期
                if (AuthUtil.isLogin()) {
                    UserDto user = AuthUtil.getUser();
                    LocalDateTime modifyDate = null == user.getLastModify() ?
                            user.getCreateDate() : user.getLastModify();
                    Long expSec = SysConfigUtil.isExp(modifyDate);
                    if (null != expSec) {
                        return FrResult.expPwd((FrResult) body, expSec);
                    } else {
                        return body;
                    }
                } else {
                    return body;
                }
            }
            if(body instanceof SaResult){
                return FrResult.success(body);
            }
            return JSONObject.toJSONString(FrResult.success(body, "操作成功"));
        }
    }
}
