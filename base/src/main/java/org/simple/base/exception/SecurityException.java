package org.simple.base.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class SecurityException extends Exception{

    /**
     * 异常信息
     */
    private String errorMessage;

    /**
     * 自定义异常
     *
     * @param msg 异常信息
     */
    public SecurityException(String msg) {
        this.errorMessage = msg;
    }

    /**
     * 获取异常信息
     *
     * @return 异常信息
     */
    public String getErrorMessage() {
        return this.errorMessage;
    }

    @Override
    public Throwable fillInStackTrace() {
        return this;
    }
}
