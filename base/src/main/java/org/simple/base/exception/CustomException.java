package org.simple.base.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.simple.base.enums.ResultCodeEnum;

/**
 * 自定义异常封装
 *
 * <AUTHOR>
 * @version v1.0
 * @since 2022/7/11
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CustomException extends Exception {

    /**
     * 异常编码
     */
    private Object errorCode;

    /**
     * 异常信息
     */
    private String errorMessage;

    /**
     * 自定义异常
     *
     * @param errorCode 异常编码
     */
    public CustomException(Object errorCode) {
        ResultCodeEnum codesEnum = ResultCodeEnum.fromCode(errorCode.toString());
        if (codesEnum != null) {
            this.errorCode = codesEnum.getCode();
            this.errorMessage = codesEnum.getMsg();
        } else {
            this.errorCode = ResultCodeEnum.FAILED.getCode();
            this.errorMessage = ResultCodeEnum.FAILED.getMsg();
        }
    }

    /**
     * 获取异常信息
     *
     * @return 异常信息
     */
    public String getErrorMessage() {
        return this.errorMessage;
    }

    @Override
    public Throwable fillInStackTrace() {
        return this;
    }
}
