package org.simple.base.properties;


import lombok.Data;
import org.simple.base.dto.RateLimit;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 限流配置
 * */
@Data
@Component
@ConfigurationProperties(prefix = "limitcfg.base")
public class RateLimitProperties {
    private RateLimit pub;
    private List<RateLimit> pri;
    private boolean enabled;
}
