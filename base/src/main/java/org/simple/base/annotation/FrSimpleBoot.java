package org.simple.base.annotation;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE})
@EnableAsync
@MapperScan("org.simple.*.mapper")
@ServletComponentScan(basePackages = {"org.simple.center.filter"})
@SpringBootApplication(scanBasePackages = "org.simple")
public @interface FrSimpleBoot {
    String[] value() default {};
}
