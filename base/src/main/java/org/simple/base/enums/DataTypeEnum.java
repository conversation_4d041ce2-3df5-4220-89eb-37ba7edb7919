package org.simple.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum DataTypeEnum {
    ORACLE("oracle", "oracle.jdbc.driver.OracleDriver"),
    MYSQL("mysql", "com.mysql.cj.jdbc.Driver");
    private final String type;
    private final String driver;

    /**
     * 根据枚举key获取枚举
     *
     * @param typeKey 编号
     * @return 枚举
     */
    public static DataTypeEnum getByType(String typeKey) {
        return Arrays.stream(values()).filter(v -> v.getType().equals(typeKey)).findFirst().orElse(null);
    }
}
