package org.simple.base.enums;

import lombok.Getter;

@Getter
public enum SaNoLoginResult {
    /**
     * token为空
     **/
    NO_AUTH_EXCEPTION(403, "禁止访问"),
    /**
     * token为空
     **/
    NOT_TOKEN_EXCEPTION(403, "没有访问权限"),
    /**
     * token无效
     **/
    INVALID_EXCEPTION(401, "token无效"),
    /**
     * token过期
     **/
    TOKEN_TINEDOUT_EXCEPTION(401, "token已过期"),
    /**
     * 用户被顶下线
     **/
    BE_REPLACED_EXCEPTION(401, "用户被顶下线"),
    /**
     * 用户被踢下线
     **/
    KICK_OUT_EXCEPTION(401, "用户被踢下线"),
    /**
     * 用户被踢下线
     **/
    NOT_LOGIN_EXCEPTION(401, "当前会话未登录"),

    /**
     * 没有相关权限
     */
    FORBIDDEN(403, "没有相关权限");

    private final long code;
    private final String msg;

    SaNoLoginResult(long code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
