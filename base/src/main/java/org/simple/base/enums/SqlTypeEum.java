package org.simple.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum SqlTypeEum {
    MENU("menu", "center_menu", "id", "update_center_menu_"),
    PARAMETER("parameter", "center_sysparameter", "id", "update_center_sysparameter_"),
    DICT("dict", "center_dictionary", "code", "update_center_dictionary_"),
    OPENAPI("openapi", "api_main", "no", "update_api_main_"),
    MODEL("model", "code_form_template,code_form_field", "id,template_id", "update_form_template_"),
    MODEL_TYPE("model_type", "code_form_type", "id", "update_form_type_"),
    DATA_MODEL_TYPE("data_model_type", "code_model_type", "id", "update_code_model_type_"),
    DATA_MODEL("data_model", "code_model,code_model_req,code_model_out,code_model_button", "id,model_id,model_id,model_id", "update_code_model_");
    private final String type;
    private final String table;
    private final String primary;
    private final String description;

    /**
     * 根据枚举key获取枚举
     *
     * @param typeKey 编号
     * @return 枚举
     */
    public static SqlTypeEum getTable(String typeKey) {
        return Arrays.stream(values()).filter(v -> v.getType().equals(typeKey)).findFirst().orElse(null);
    }
}


