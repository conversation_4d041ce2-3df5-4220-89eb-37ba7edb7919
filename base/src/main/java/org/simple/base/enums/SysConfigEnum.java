package org.simple.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum SysConfigEnum {
    PWD_COMPLEXITY("pwdComplexity",
            "^\\S*(?=\\S{6,})(?=\\S*\\d)(?=\\S*[A-Z])(?=\\S*[a-z])(?=\\S*[!@#$%^&*? ])\\S*$"),
    PWD_VALID("pwdValid", "30"),
    PWD_REPEAT("pwdRepeat", "3"),
    PWD_VALID_DAY("pwdValidDay", "10"),
    PWD_VALID_MSG("pwdValidMsg", "至少六位，需包含大小写字，数字和特殊字符"),
    LOGIN_TYPE("loginType", "password,phone"),
    LOGIN_ACCOUNT("loginAccount", "user,email,phone"),
    LOGIN_ERROR("loginError", "5"),
    LOGIN_CODE("loginCode", "true"),
    LOGIN_LOCK("loginLock", "5");
    private final String label;
    private final String value;

    /**
     * 根据枚举key获取枚举
     *
     * @param key 编号
     * @return 枚举
     */
    public static SysConfigEnum getValue(String key) {
        return Arrays.stream(values()).filter(v -> v.getLabel().equals(key)).findFirst().orElse(null);
    }
}
