package org.simple.base.controller;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.simple.base.constant.ModelConstant;
import org.simple.base.param.FrQuickFormParam;
import org.simple.base.service.FrCodeQuickService;
import org.simple.base.util.DataModelUtil;
import org.simple.base.util.FormModelUtil;
import org.simple.base.vo.FrResult;
import org.simple.base.vo.model.DataModelVo;
import org.simple.base.vo.model.FieldModelVo;
import org.simple.base.vo.model.FormModelVo;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 页面模型接口
 */
@RestController
@RequestMapping("/center/frQuickCode")
@Slf4j
public class FrQuickCodeController {

    @Resource
    private FrCodeQuickService frCodeQuickService;

    @PostMapping("/formData")
    @Operation(summary = "查询表单数据")
    public FrResult<?> formData(@RequestBody FrQuickFormParam params) {
        FormModelVo formModelVo = FormModelUtil.getFormModel(params.getFormId());
        return FrResult.success(
                frCodeQuickService.getFormData(formModelVo.getDbName(),
                        params.getFormId(), params.getBusinessKey())
        );
    }

    @PostMapping("model")
    public FrResult<?> model(@RequestBody FrQuickFormParam param) {
        try {
            String modelId = param.getFormId();
            Assert.notNull(modelId, "参数 " +
                    ModelConstant.MODEL_ID + " 不能为空");
            FormModelVo formModelVo = FormModelUtil.getFormModel(modelId);
            if (StrUtil.isEmpty(formModelVo.getId())) {
                throw new Exception("模型不存在或未启用");
            }
            return FrResult.success(formModelVo);
        } catch (Exception ex) {
            return FrResult.failed(ex.getMessage());
        }
    }

    @PostMapping("/saveOrEdit")
    @Operation(summary = "新增/修改表单数据")
    public FrResult<?> saveOrEdit(@RequestBody FrQuickFormParam params) {
        try {
            FormModelVo formModelVo = FormModelUtil.getFormModel(params.getFormId());
            if (null == formModelVo) {
                throw new Exception("模型不存在或不可用");
            }
            if (StrUtil.isNotEmpty(params.getBusinessKey())) {
                if (frCodeQuickService.isHaveData(formModelVo.getDbName(),
                        params.getFormId(), params.getBusinessKey())) {
                    frCodeQuickService.dealEditForm(formModelVo.getDbName(),
                            params.getFormId(), params.getBusinessKey(), params.getFormData());
                } else {
                    frCodeQuickService.dealInsertForm(formModelVo.getDbName(),
                            params.getFormId(), params.getBusinessKey(), params.getFormData());
                }
            } else {
                String businessKey = RandomUtil.randomString(64);
                String primaryField = "";
                for (FieldModelVo vo : formModelVo.getFieldList()) {
                    if (StrUtil.isNotEmpty(vo.getFieldNo())
                            && !vo.getType().equals("data_01")
                            && vo.getFieldNo().equals(formModelVo.getPrimaryKey())) {
                        primaryField = vo.getId();
                        break;
                    }
                }
                if (StrUtil.isNotEmpty(primaryField)
                        && params.getFormData().containsKey(primaryField)
                        && null != params.getFormData().get(primaryField)) {
                    businessKey = params.getFormData().getString(primaryField);
                }
                frCodeQuickService.dealInsertForm(formModelVo.getDbName(),
                        params.getFormId(), businessKey, params.getFormData());
            }
            return FrResult.success();
        } catch (Exception e) {
            return FrResult.failed(e.getMessage());
        }
    }

    @PostMapping("/del")
    @Operation(summary = "删除表单数据")
    public FrResult<?> del(@RequestBody FrQuickFormParam params) {
        try {
            DataModelVo dataModelVo = DataModelUtil.getDataModel(params.getFormId());
            if (null == dataModelVo) {
                throw new Exception("模型不存在或不可用");
            }
            JSONArray btnArray = new JSONArray();
            for (int i = 0; i < dataModelVo.getBtnData().size(); i++) {
                JSONObject btn = dataModelVo.getBtnData().getJSONObject(i);
                if (btn.getString("id").equals(params.getBtnId())) {
                    btnArray = btn.getJSONArray("delConfig");
                    break;
                }
            }
            frCodeQuickService.dealDelForm(dataModelVo.getDbName(), btnArray, params.getBusinessKey());
            return FrResult.success();
        } catch (Exception e) {
            return FrResult.failed(e.getMessage());
        }
    }
}
