package org.simple.base.controller;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.simple.base.constant.ModelConstant;
import org.simple.base.dto.CreateSqlDto;
import org.simple.base.dto.DataSourceDto;
import org.simple.base.enums.DataTypeEnum;
import org.simple.base.enums.SqlTypeEum;
import org.simple.base.service.FrQuickService;
import org.simple.base.util.AuthUtil;
import org.simple.base.util.DataModelUtil;
import org.simple.base.vo.FrResult;
import org.simple.base.vo.model.DataModelVo;
import org.simple.base.vo.model.ModelParam;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.sql.DataSource;
import java.io.IOException;
import java.sql.DriverManager;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;


/**
 * 敏捷开发接口
 */
@RestController
@RequestMapping("/center/frQuick")
@Slf4j
public class FrQuickController {

    private static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(FrQuickController.class);

    @Resource
    private DataSource dataSource;

    @Resource
    private FrQuickService frQuickService;

    @PostMapping("list")
    public FrResult<?> list(@RequestBody ModelParam param) {
        try {
            String modelId = param.getString(ModelConstant.MODEL_ID);
            Assert.notNull(modelId, "参数 " +
                    ModelConstant.MODEL_ID + " 不能为空");
            DataModelVo dataModelVo = DataModelUtil.getDataModel(modelId);

            if (StrUtil.isEmpty(dataModelVo.getId())) {
                throw new Exception("模型不存在或未启用");
            }
            //判断当前用户是否有权限调用
            String isPublic = dataModelVo.getIsPublic();
            if (isPublic.equals("1")) {
                List<String> permissions = AuthUtil.getPermissions();
                List<String> roleGroup = StrUtil.split(dataModelVo.getRoleGroup(), ",");
                boolean isAuth = false;
                for (String auth : roleGroup) {
                    if (permissions.contains(auth)) {
                        isAuth = true;
                        break;
                    }
                }
                if (!isAuth) {
                    throw new Exception("无权操作");
                }
            }

            //判断数据源是否存在
            if (!isHave(dataModelVo.getDbName())) {
                throw new Exception("数据源不存在");
            }
            //判断请求参数是否有必填项
            JSONArray req = dataModelVo.getReqData();
            for (int i = 0; i < req.size(); i++) {
                JSONObject reqObj = req.getJSONObject(i);
                JSONObject extAttr = reqObj.getJSONObject("extAttr");
                String attrNo = reqObj.getString("attrNo");
                boolean required = extAttr.getBooleanValue("required");
                if (required && (null == param.get(attrNo) || StrUtil.isEmpty(param.get(attrNo).toString()))) {
                    throw new Exception("参数" + reqObj.getString("attrNo") + "必须传入");
                }
            }
            //开始查询
            //先判断是否分页
            String isPage = dataModelVo.getOutType();
            if (isPage.equals("01")) { //单条数据
                return FrResult.success(frQuickService.getOne(dataModelVo.getDbName(), dataModelVo, param));
            } else if (isPage.equals("02")) { //多条数据
                return FrResult.success(frQuickService.list(dataModelVo.getDbName(), dataModelVo, param));
            } else if (isPage.equals("03")) { //分页数据
                long current = null == param.get("current") ? 1 : param.getLongValue("current");
                long size = null == param.get("size") ? 20 : param.getLongValue("size");
                Page<JSONObject> page = new Page<>(current, size);
                return FrResult.success(frQuickService.pageList(dataModelVo.getDbName(), dataModelVo, page, param));
            }

        } catch (Exception ex) {
            log.error("调用报错：", ex);
            return FrResult.failed(ex.getMessage());
        }
        return FrResult.failed("未知错误");
    }

    @PostMapping("model")
    public FrResult<?> model(@RequestBody ModelParam param) {
        try {
            String modelId = param.getString(ModelConstant.MODEL_ID);
            Assert.notNull(modelId, "参数 " +
                    ModelConstant.MODEL_ID + " 不能为空");
            DataModelVo dataModelVo = DataModelUtil.getDataModel(modelId);
            if (StrUtil.isEmpty(dataModelVo.getId())) {
                throw new Exception("模型不存在或未启用");
            }
            return FrResult.success(dataModelVo);
        } catch (Exception ex) {
            return FrResult.failed(ex.getMessage());
        }
    }

    private boolean isHave(String dbName) {
        DynamicRoutingDataSource ds = (DynamicRoutingDataSource) dataSource;
        if (ds.getDataSources().keySet().contains(dbName)) {
            return true;
        }
        return false;
    }

    /**
     * 判断当前服务数据源是否可用
     */
    @PostMapping("isUse")
    public FrResult<?> isUse(@RequestBody DataSourceDto dto) {
        return FrResult.success(!isHave(dto.getDataName()));
    }

    /**
     * 测试数据库连接
     */
    @PostMapping("checkCon")
    public FrResult<?> checkCon(@RequestBody DataSourceDto d) {
        String url = d.getUrl();
        String pwd = d.getPwd();
        String user = d.getUser();
        DataTypeEnum dataTypeEnum = DataTypeEnum.getByType(d.getType());
        String driver = dataTypeEnum != null ? dataTypeEnum.getDriver() : "com.mysql.cj.jdbc.Driver";
        try {
            Class.forName(driver);
            DriverManager.getConnection(url, user, pwd);
        } catch (Exception ex) {
            log.error("数据源连接失败：", ex);
            return FrResult.success(false);
        }
        return FrResult.success(true);
    }

    /**
     * 导出SQL升级脚本
     */
    @PostMapping("createSql")
    public void codeCreate(@RequestBody CreateSqlDto dto,
                           HttpServletResponse response) throws IOException {
        Assert.notNull(dto.getType(), "请选择要导出的数据类型");
        Assert.notNull(dto.getValue(), "请选择要导出的数据");
        SqlTypeEum sqlTypeEum = SqlTypeEum.getTable(dto.getType());
        byte[] data = frQuickService.createSql(sqlTypeEum.getTable(),
                sqlTypeEum.getPrimary(), dto.getValue());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String fileName = sqlTypeEum.getDescription() + sdf.format(new Date()) + ".sql";
        response.reset();
        response.setHeader("Content-Disposition", URLUtil.encode(fileName));
        response.setContentType("application/sql; charset=UTF-8");
        IoUtil.write(response.getOutputStream(), Boolean.TRUE, data);
    }
}