package org.simple.base.config;

import jakarta.annotation.Resource;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import org.simple.base.handler.WebInterceptorHandler;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Nonnull;
import java.util.List;

/**
 * web过滤器
 *
 * <AUTHOR>
 * @version v1.0
 * @since 2022/7/11
 */
@Getter
@Configuration
@RequiredArgsConstructor
@ConfigurationProperties(prefix = "auth")
public class WebFilterConfig implements WebMvcConfigurer {

    /**
     * 排除拦截的url
     */
    @Setter
    private List<String> whiteUrl;

    @Resource
    private WebInterceptorHandler webInterceptorHandler;

    /**
     * 跨域配置
     *
     * @param registry 配置属性
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns("*")
                .allowedHeaders("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE")
                .allowCredentials(true)
                .maxAge(3600);
    }

    /**
     * 自定拦截器
     *
     * @param registry 配置属性
     */
    @Override
    public void addInterceptors(@Nonnull InterceptorRegistry registry) {
        WebMvcConfigurer.super.addInterceptors(registry);
        registry.addInterceptor(webInterceptorHandler)
                .addPathPatterns("/**").excludePathPatterns(whiteUrl);
    }

}
