package org.simple.base.config.satoken;

import cn.dev33.satoken.context.SaHolder;
import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.jwt.StpLogicJwtForSimple;
import cn.dev33.satoken.oauth2.data.model.AccessTokenModel;
import cn.dev33.satoken.oauth2.template.SaOAuth2Util;
import cn.dev33.satoken.stp.StpLogic;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Getter;
import lombok.Setter;
import org.simple.base.constant.AuthConstant;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

@Setter
@Configuration
@ConfigurationProperties(prefix = "auth")
@Getter
public class SaTokenConfig implements WebMvcConfigurer {


    /**
     * Sa-Token 整合 jwt (Simple 简单模式)
     */
    @Bean
    public StpLogic getStpLogicJwt() {
        return new StpLogicJwtForSimple();
    }


    //url白名单
    private List<String> whiteUrl;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册 Sa-Token 拦截器，打开注解式鉴权功能
        registry
                .addInterceptor(new SaInterceptor()
                        .setAuth(o -> {
                            //检测是否携带accessToken
                            String accessToken = SaHolder.getRequest().
                                    getHeader(AuthConstant.OAUTH2_TOKEN_NAME);
                            if (StrUtil.isEmpty(accessToken)) {
                                throw new NotLoginException("access_token无效或已过期", "",
                                        NotLoginException.TOKEN_FREEZE);
                            }
                            // 验证 access-token 是否有效
                            AccessTokenModel acTokenEntity =
                                    SaOAuth2Util.getAccessToken(accessToken);
                            if (null == acTokenEntity) {
                                throw new NotLoginException("access_token无效或已过期", "",
                                        NotLoginException.TOKEN_FREEZE);
                            }

                            StpUtil.setTokenValue(StpUtil.
                                    getTokenValueByLoginId(acTokenEntity.loginId));
                            //检验active-token是否过期
                            try {
                                StpUtil.checkActiveTimeout();
                            } catch (NotLoginException ex) {
                                StpUtil.logout();
                                SaOAuth2Util.revokeAccessToken(accessToken);
                                throw ex;
                            }
                        })).addPathPatterns("/**")
                .excludePathPatterns(splitUrls());
    }

    private String[] splitUrls() {
        whiteUrl.add("/error");
        whiteUrl.add("/login");
        whiteUrl.add("/oauth2/**");
        whiteUrl.add("/assets/**");
        whiteUrl.add("/js/**");
        whiteUrl.add("/code");
        whiteUrl.add("/sms");
        return CollectionUtil.join(whiteUrl, ",").split(",");
    }

}
