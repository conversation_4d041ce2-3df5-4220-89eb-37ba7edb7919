package org.simple.base.config.redisson;


import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBlockingQueue;
import org.redisson.api.RedissonClient;
import org.simple.base.enums.RedisDelayQueueEnum;
import org.simple.base.handler.RedisDelayQueueHandler;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Configuration;


/**
 * @author: frSimple
 * @date: 2023/04/20
 * @Description: redis延迟队列配置
 */

@Slf4j
@Configuration
@EnableAutoConfiguration
public class RedisDelayQueueConfig implements CommandLineRunner {

    private static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(RedisDelayQueueConfig.class);

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private ApplicationContext applicationContext;

    @Override
    public void run(String... args) {
        RedisDelayQueueEnum[] redisDelayQueueArr = RedisDelayQueueEnum.values();
        for (RedisDelayQueueEnum queueEnum : redisDelayQueueArr) {
            RBlockingQueue<Object> blockingFairQueue = redissonClient.getBlockingQueue(queueEnum.getQueueName());
            // 由于此线程需要常驻，可以新建线程，不用交给线程池管理
            Thread thread = new Thread(() -> {
                log.info("启动监听延迟队列线程:{}", queueEnum.getQueueName());
                while (true) {
                    try {
                        redissonClient.getDelayedQueue(blockingFairQueue);
                        // 获取到执行类
                        RedisDelayQueueHandler redisDelayQueueHandler = (RedisDelayQueueHandler) applicationContext.getBean(queueEnum.getBeanId());
                        // 获取到被执行对象，作为参数传递给redisDelayQueueHandler
                        Object t = blockingFairQueue.take();
                        log.info("监听延迟队列线程:{}, 获取到值:{}", queueEnum.getQueueName(), JSONObject.toJSONString(t));
                        redisDelayQueueHandler.execute(t);
                    } catch (Exception e) {
                        log.error("监听延迟队列线程错误,", e);
                        try {
                            Thread.sleep(10000);
                        } catch (InterruptedException ignored) {
                        }
                    }
                }
            });
            thread.setName(queueEnum.getQueueName());
            thread.start();
        }
    }
}
