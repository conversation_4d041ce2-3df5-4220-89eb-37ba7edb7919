package org.simple.base.config.mybatisplus;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import lombok.RequiredArgsConstructor;
import org.apache.ibatis.reflection.MetaObject;
import org.simple.base.constant.CurrentConst;
import org.simple.base.util.AuthUtil;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * MyBatisPlus字段自动填充
 *
 * <AUTHOR>
 * @version v1.0
 * @since 2022/7/7
 */
@Component
@RequiredArgsConstructor
public class MyMetaObjectHandler implements MetaObjectHandler {

    /**
     * 新增自动填充字段
     *
     * @param metaObject 元对象
     */
    @Override
    public void insertFill(MetaObject metaObject) {
        if(StpUtil.isLogin()){
            this.strictInsertFill(metaObject, CurrentConst.CREATOR, AuthUtil.getUser()::getId, String.class);
            this.strictInsertFill(metaObject, CurrentConst.TENANT_ID, AuthUtil.getUser()::getTenantId, String.class);
        }
        this.strictInsertFill(metaObject, CurrentConst.CREATE_TIME, LocalDateTime::now,LocalDateTime.class);
        this.strictInsertFill(metaObject, CurrentConst.UPDATE_TIME, LocalDateTime::now,LocalDateTime.class);
    }

    /**
     * 更新自动填充字段
     *
     * @param metaObject 元对象
     */
    @Override
    public void updateFill(MetaObject metaObject) {
        this.strictUpdateFill(metaObject, CurrentConst.UPDATE_TIME, LocalDateTime::now,LocalDateTime.class);
    }
}
