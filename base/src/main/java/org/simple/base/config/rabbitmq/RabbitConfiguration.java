package org.simple.base.config.rabbitmq;

import org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnProperty(value = "spring.rabbitmq.enable",havingValue = "true")
public class RabbitConfiguration extends RabbitAutoConfiguration {
}
