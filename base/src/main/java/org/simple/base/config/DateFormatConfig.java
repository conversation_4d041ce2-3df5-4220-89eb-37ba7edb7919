package org.simple.base.config;

import com.fasterxml.jackson.databind.Module;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.TimeZone;

/**
 * <AUTHOR>
 * @version v1.0
 * @since 2022/11/27
 */
@Configuration
public class DateFormatConfig {
    @Value("${spring.jackson.local-date-time-format:yyyy-MM-dd HH:mm:ss}")
    String localDateTimeFormat;

    @Value("${spring.jackson.local-date-format:yyyy-MM-dd}")
    String localDateFormat;

    @Bean
    public Jackson2ObjectMapperBuilderCustomizer jackson2ObjectMapperBuilderCustomizer() {
        return builder -> {
            String dateTimeFormat = "yyyy-MM-dd HH:mm:ss";
            builder.dateFormat(new SimpleDateFormat(dateTimeFormat));
            builder.timeZone(TimeZone.getDefault());
            //识别Java8时间
            List<Module> modules = new ArrayList<>();
            modules.add(new Jdk8Module());
            JavaTimeModule javaTimeModule = new JavaTimeModule();
            javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(DateTimeFormatter.ofPattern(localDateTimeFormat)))
                    .addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(DateTimeFormatter.ofPattern(localDateTimeFormat)))
                    .addSerializer(LocalDate.class,new LocalDateSerializer(DateTimeFormatter.ofPattern(localDateFormat)))
                    .addDeserializer(LocalDate.class,new LocalDateDeserializer(DateTimeFormatter.ofPattern(localDateFormat)));
            modules.add(javaTimeModule);
            builder.modulesToInstall(modules.toArray(new Module[modules.size()]));
        };
    }
}


