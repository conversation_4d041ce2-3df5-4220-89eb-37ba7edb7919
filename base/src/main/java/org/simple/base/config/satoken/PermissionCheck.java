package org.simple.base.config.satoken;

import cn.dev33.satoken.stp.StpInterface;
import org.simple.base.constant.RedisConstant;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
public class PermissionCheck implements StpInterface {

    @Resource
    private RedisTemplate<String, List<String>> redisTemplate;

    /**
     * 返回一个账号所拥有的权限码集合
     */
    @Override
    public List<String> getPermissionList(Object loginId, String loginType) {
        if (Boolean.TRUE.equals(redisTemplate.hasKey(RedisConstant.AUTH_PERMISSION_PIX + loginId))) {
            return
                    redisTemplate.opsForValue().get(RedisConstant.AUTH_PERMISSION_PIX + loginId);
        } else {
            return new ArrayList<>();
        }
    }

    /**
     * 返回一个账号所拥有的角色标识集合 (权限与角色可分开校验)
     */
    @Override
    public List<String> getRoleList(Object loginId, String loginType) {
        if (Boolean.TRUE.equals(redisTemplate.hasKey(RedisConstant.AUTH_ROLE_PIX + loginId))) {
            return redisTemplate.opsForValue().get(RedisConstant.AUTH_ROLE_PIX + loginId);
        } else {
            return new ArrayList<>();
        }
    }
}
