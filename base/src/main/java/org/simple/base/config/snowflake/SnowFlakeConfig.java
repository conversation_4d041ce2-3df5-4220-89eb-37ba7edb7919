package org.simple.base.config.snowflake;

import com.github.yitter.contract.IdGeneratorOptions;
import com.github.yitter.idgen.YitIdHelper;
import lombok.RequiredArgsConstructor;
import org.simple.base.properties.SnowFlakeProperties;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.Resource;

/**
 * SnowFlakeConfig
 *
 * <AUTHOR>
 * @version v1.0
 * @since 2022/7/9
 */
@Configuration
@RequiredArgsConstructor
public class SnowFlakeConfig implements ApplicationRunner {

    @Resource
    private SnowFlakeProperties snowFlakeProperties;

    /**
     * 自增雪花id注册配置
     *
     * @param args 参数
     */
    @Override
    public void run(ApplicationArguments args) {
        IdGeneratorOptions idGeneratorOptions = new IdGeneratorOptions(
                snowFlakeProperties.getWorkerId());
        YitIdHelper.setIdGenerator(idGeneratorOptions);
    }
}
