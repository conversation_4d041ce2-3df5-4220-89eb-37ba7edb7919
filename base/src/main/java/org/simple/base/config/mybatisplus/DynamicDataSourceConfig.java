package org.simple.base.config.mybatisplus;

import com.baomidou.dynamic.datasource.creator.DataSourceProperty;
import com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator;
import com.baomidou.dynamic.datasource.provider.AbstractJdbcDataSourceProvider;
import com.baomidou.dynamic.datasource.provider.DynamicDataSourceProvider;
import com.zaxxer.hikari.HikariDataSource;
import jakarta.annotation.Resource;
import org.simple.base.enums.DataTypeEnum;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.HashMap;
import java.util.Map;

@Configuration
public class DynamicDataSourceConfig {

    @Value("${spring.application.name}")
    private String applicationName;

    @Value("${spring.datasource.dynamic.datasource.master.url}")
    private String url;

    @Value("${spring.datasource.dynamic.datasource.master.username}")
    private String username;

    @Value("${spring.datasource.dynamic.datasource.master.password}")
    private String password;

    @Resource
    private DefaultDataSourceCreator defaultDataSourceCreator;

    @Bean
    public DynamicDataSourceProvider jdbcDynamicDataSourceProvider() {
        return new AbstractJdbcDataSourceProvider(
                defaultDataSourceCreator,
                url, username, password) {
            @Override
            protected Map<String, DataSourceProperty> executeStmt(Statement statement) throws SQLException {
                PreparedStatement prepareStatement =
                        statement.getConnection().prepareStatement("select * from code_datasource where app_name = ?");
                prepareStatement.setString(1, applicationName);
                ResultSet rs = prepareStatement.executeQuery();
                Map<String, DataSourceProperty> result = new HashMap<>();
                while (rs.next()) {
                    String name = rs.getString("data_name");
                    String username = rs.getString("user");
                    String password = rs.getString("pwd");
                    String url = rs.getString("url");
                    DataTypeEnum dataTypeEnum = DataTypeEnum.getByType(rs.getString("type"));
                    String driver = dataTypeEnum != null ? dataTypeEnum.getDriver() : "com.mysql.cj.jdbc.Driver";
                    DataSourceProperty property = new DataSourceProperty();
                    property.setUsername(username);
                    property.setPassword(password);
                    property.setUrl(url);
                    property.setDriverClassName(driver);
                    property.setType(HikariDataSource.class);
                    result.put(name, property);
                }
                return result;
            }
        };
    }
}
