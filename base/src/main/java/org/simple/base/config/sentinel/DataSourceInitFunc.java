package org.simple.base.config.sentinel;

import com.alibaba.csp.sentinel.datasource.Converter;
import com.alibaba.csp.sentinel.datasource.ReadableDataSource;
import com.alibaba.csp.sentinel.datasource.redis.RedisDataSource;
import com.alibaba.csp.sentinel.datasource.redis.config.RedisConnectionConfig;
import com.alibaba.csp.sentinel.init.InitFunc;
import com.alibaba.csp.sentinel.slots.block.authority.AuthorityRule;
import com.alibaba.csp.sentinel.slots.block.authority.AuthorityRuleManager;
import com.alibaba.csp.sentinel.slots.block.degrade.DegradeRule;
import com.alibaba.csp.sentinel.slots.block.degrade.DegradeRuleManager;
import com.alibaba.csp.sentinel.slots.block.flow.FlowRule;
import com.alibaba.csp.sentinel.slots.block.flow.FlowRuleManager;
import com.alibaba.csp.sentinel.slots.block.flow.param.ParamFlowRule;
import com.alibaba.csp.sentinel.slots.block.flow.param.ParamFlowRuleManager;
import com.alibaba.csp.sentinel.slots.system.SystemRule;
import com.alibaba.csp.sentinel.slots.system.SystemRuleManager;
import com.alibaba.fastjson2.JSON;

import java.util.ArrayList;
import java.util.List;

public class DataSourceInitFunc implements InitFunc {

    public static final String RULE_FLOW_PREFIX = "sentinel:rule:flow";

    public static final String RULE_FLOW_CHANNEL_PREFIX = "sentinel:channel:flow";

    // 最终规则是存储的key
    public static final String RULE_DEGRADE_PREFIX = "sentinel:rule:degrade";

    // Redis的订阅发布功能，需要一个通道
    public static final String RULE_DEGRADE_CHANNEL_PREFIX = "sentinel:channel:degrade";

    // 最终规则是存储的key
    public static final String RULE_PARAM_PREFIX = "sentinel:rule:param";

    // Redis的订阅发布功能，需要一个通道
    public static final String RULE_PARAM_CHANNEL_PREFIX = "sentinel:channel:param";

    // 最终规则是存储的key
    public static final String RULE_SYSTEM_PREFIX = "sentinel:rule:system";

    // Redis的订阅发布功能，需要一个通道
    public static final String RULE_SYSTEM_CHANNEL_PREFIX = "sentinel:channel:system";

    // 最终规则是存储的key
    public static final String RULE_AUTH_PREFIX = "sentinel:rule:auth";

    // Redis的订阅发布功能，需要一个通道
    public static final String RULE_AUTH_CHANNEL_PREFIX = "sentinel:channel:auth";


    @Override
    public void init() throws Exception {

        Converter<String, List<FlowRule>> parser = source -> {
            List<FlowRule> flowRules = new ArrayList<>();
            if (source != null) {
                String replace = source.replace("\\", "");
                String substring = replace.substring(1, replace.length() - 1);
                flowRules = JSON.parseArray(substring, FlowRule.class);
            }
            return flowRules;
        };
        RedisConnectionConfig config = null;
        if (RedisConfigProperties.PASSWORD.equals("未定义")) {
            config = RedisConnectionConfig.builder()
                    .withHost(RedisConfigProperties.HOST)
                    .withPort(RedisConfigProperties.PORT)
                    .withDatabase(RedisConfigProperties.DATABASE)
                    .build();
        } else {
            config = RedisConnectionConfig.builder()
                    .withHost(RedisConfigProperties.HOST)
                    .withPort(RedisConfigProperties.PORT)
                    .withPassword(RedisConfigProperties.PASSWORD)
                    .withDatabase(RedisConfigProperties.DATABASE)
                    .build();
        }
        ReadableDataSource<String, List<FlowRule>> flowRuleDataSource
                = new RedisDataSource<>(config, RULE_FLOW_PREFIX, RULE_FLOW_CHANNEL_PREFIX, parser);
        FlowRuleManager.register2Property(flowRuleDataSource.getProperty());

        Converter<String, List<DegradeRule>> parser1 = source -> {
            List<DegradeRule> flowRules = new ArrayList<>();
            if (source != null) {
                String replace = source.replace("\\", "");
                String substring = replace.substring(1, replace.length() - 1);
                flowRules = JSON.parseArray(substring, DegradeRule.class);
            }
            return flowRules;
        };
        ReadableDataSource<String, List<DegradeRule>> degradeDataSource
                = new RedisDataSource<>(config, RULE_DEGRADE_PREFIX, RULE_DEGRADE_CHANNEL_PREFIX, parser1);
        DegradeRuleManager.register2Property(degradeDataSource.getProperty());


        Converter<String, List<SystemRule>> parser2 = source -> {
            List<SystemRule> flowRules = new ArrayList<>();
            if (source != null) {
                String replace = source.replace("\\", "");
                String substring = replace.substring(1, replace.length() - 1);
                flowRules = JSON.parseArray(substring, SystemRule.class);
            }
            return flowRules;
        };
        ReadableDataSource<String, List<SystemRule>> systemDataSource
                = new RedisDataSource<>(config, RULE_SYSTEM_PREFIX,
                RULE_SYSTEM_CHANNEL_PREFIX, parser2);
        SystemRuleManager.register2Property(systemDataSource.getProperty());

        Converter<String, List<ParamFlowRule>> parser3 = source -> {
            List<ParamFlowRule> flowRules = new ArrayList<>();
            if (source != null) {
                String replace = source.replace("\\", "");
                String substring = replace.substring(1, replace.length() - 1);
                flowRules = JSON.parseArray(substring, ParamFlowRule.class);
            }
            return flowRules;
        };
        ReadableDataSource<String, List<ParamFlowRule>> paramDataSource
                = new RedisDataSource<>(config, RULE_PARAM_PREFIX,
                RULE_PARAM_CHANNEL_PREFIX, parser3);
        ParamFlowRuleManager.register2Property(paramDataSource.getProperty());


        Converter<String, List<AuthorityRule>> parser4 = source -> {
            List<AuthorityRule> flowRules = new ArrayList<>();
            if (source != null) {
                String replace = source.replace("\\", "");
                String substring = replace.substring(1, replace.length() - 1);
                flowRules = JSON.parseArray(substring, AuthorityRule.class);
            }
            return flowRules;
        };
        ReadableDataSource<String, List<AuthorityRule>> authDataSource
                = new RedisDataSource<>(config, RULE_AUTH_PREFIX,
                RULE_AUTH_CHANNEL_PREFIX, parser4);
        AuthorityRuleManager.register2Property(authDataSource.getProperty());
    }
}
