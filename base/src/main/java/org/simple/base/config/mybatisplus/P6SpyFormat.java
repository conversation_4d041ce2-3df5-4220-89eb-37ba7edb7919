package org.simple.base.config.mybatisplus;

import com.p6spy.engine.spy.appender.MessageFormattingStrategy;
import lombok.extern.slf4j.Slf4j;


@Slf4j
public class P6SpyFormat implements MessageFormattingStrategy {
    @Override
    public String formatMessage(
            int connectionId, String now, long elapsed, String category, String prepared, String sql, String url
    ) {
        return "执行耗时：" + elapsed + " ms >>> SQL语句：" + sql.replaceAll("[\\s]+", " ");
    }
}
