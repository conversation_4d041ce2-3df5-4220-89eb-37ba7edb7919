package org.simple.base.constant;

public interface RedisConstant {

    String CODE_STR = "frSimple:img:code";
    String PHONE_CODE_STR = "frSimple:phone:code";
    String PHONE_UPDATE_CODE_STR = "frSimple:phone:code:update:";
    String EMAIL_UPDATE_CODE_STR = "frSimple:email:code:update:";

    String LOCK_TIME = "frSimple:lock:";

    Integer CODE_TIMEOUT = 60;


    String ALIOSS_PIX = "frSimple:alioss";
    String TENCENT_PIX = "frSimple:tencentoss";
    String MINIO_PIX = "frSimple:miniooss";
    String BASEOSS_PIX = "frSimple:baseoss";
    String FTPOSS_PIX = "frSimple:ftposs";
    String USEOSS_PIX = "frSimple:useoss";

    String EMAIL_PIX = "frSimple:email";

    String SMS_ALI = "frSimple:sms:ali";
    String SMS_TENCENT = "frSimple:sms:tencent";

    String AUTH_PERMISSION_PIX = "frSimple:auth:permissions:";
    String AUTH_ROLE_PIX = "frSimple:auth:roles:";

    String API_AUTH_PERMISSIONS_PIX = "frSimple:api:auth:permissions:";
    String API_AUTH_WHITE_IPADDRESS_PIX = "frSimple:api:auth:white-ipaddress:";
}
