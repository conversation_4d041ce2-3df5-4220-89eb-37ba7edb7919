#!/usr/bin/env python3
import os
import re
import glob

def final_fix_entity_file(file_path):
    """最终修复单个entity文件"""
    print(f"最终修复: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 1. 添加@Serial注解（如果有serialVersionUID但没有@Serial）
    if 'serialVersionUID' in content and '@Serial' not in content:
        # 在serialVersionUID前添加@Serial
        content = re.sub(r'(\s+)(private static final long serialVersionUID)', r'\1@Serial\n\1\2', content)
    
    # 2. 添加java.io.Serial导入（如果有@Serial但没有导入）
    if '@Serial' in content and 'import java.io.Serial;' not in content:
        # 在package声明后添加导入
        package_match = re.search(r'(package [^;]+;)', content)
        if package_match:
            package_line = package_match.group(1)
            content = content.replace(package_line, package_line + '\nimport java.io.Serial;')
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"  ✅ 最终修复完成")

def main():
    # 获取所有entity文件
    entity_dir = "equipment/src/main/java/org/simple/equipment/entity"
    java_files = glob.glob(f"{entity_dir}/*.java")
    
    print(f"开始最终修复 {len(java_files)} 个entity文件...\n")
    
    for file_path in sorted(java_files):
        try:
            final_fix_entity_file(file_path)
        except Exception as e:
            print(f"  ❌ 最终修复失败: {e}")
        print()
    
    print("所有文件最终修复完成！")

if __name__ == "__main__":
    main()
