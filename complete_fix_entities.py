#!/usr/bin/env python3
import os
import re
import glob

def complete_fix_entity_file(file_path):
    """完整修复单个entity文件"""
    print(f"修复: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 1. 添加@Serial注解和serialVersionUID（如果不存在）
    if '@Serial' not in content and 'serialVersionUID' not in content:
        # 在类声明后添加
        class_pattern = r'(public class \w+[^{]*\{)'
        if re.search(class_pattern, content):
            content = re.sub(class_pattern, r'\1\n\n    @Serial\n    private static final long serialVersionUID = 1L;', content)
    
    # 2. 添加必要的导入
    imports_to_add = []
    
    if 'import java.io.Serial;' not in content and '@Serial' in content:
        imports_to_add.append('import java.io.Serial;')
    
    if 'import com.baomidou.mybatisplus.annotation.FieldFill;' not in content:
        imports_to_add.append('import com.baomidou.mybatisplus.annotation.FieldFill;')
    
    if 'import io.swagger.v3.oas.annotations.media.Schema;' not in content:
        imports_to_add.append('import io.swagger.v3.oas.annotations.media.Schema;')
    
    if 'import java.time.LocalDateTime;' not in content:
        imports_to_add.append('import java.time.LocalDateTime;')
    
    # 在package声明后添加导入
    if imports_to_add:
        package_match = re.search(r'(package [^;]+;)', content)
        if package_match:
            package_line = package_match.group(1)
            imports_str = '\n' + '\n'.join(imports_to_add)
            content = content.replace(package_line, package_line + imports_str)
    
    # 3. 添加BaseEntity字段（如果不存在）
    base_fields = [
        '    /**\n     * 创建时间\n     */\n    @TableField(fill = FieldFill.INSERT)\n    @Schema(description = "创建时间")\n    private LocalDateTime createTime;',
        '    /**\n     * 创建人id\n     */\n    @TableField(fill = FieldFill.INSERT)\n    @Schema(description = "创建人id")\n    private String creator;',
        '    /**\n     * 修改时间\n     */\n    @TableField(fill = FieldFill.INSERT_UPDATE)\n    @Schema(description = "修改时间")\n    private LocalDateTime updateTime;'
    ]
    
    # 检查是否已有这些字段
    has_create_time = 'createTime' in content
    has_creator = 'private String creator' in content
    has_update_time = 'updateTime' in content
    
    if not (has_create_time and has_creator and has_update_time):
        # 在类的最后一个字段后添加BaseEntity字段
        # 找到最后一个private字段
        private_fields = list(re.finditer(r'    private [^;]+;', content))
        
        if private_fields:
            last_field = private_fields[-1]
            fields_to_add = []
            
            if not has_create_time:
                fields_to_add.append(base_fields[0])
            if not has_creator:
                fields_to_add.append(base_fields[1])
            if not has_update_time:
                fields_to_add.append(base_fields[2])
            
            if fields_to_add:
                fields_str = '\n\n' + '\n\n'.join(fields_to_add)
                content = content[:last_field.end()] + fields_str + content[last_field.end():]
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"  ✅ 修复完成")

def main():
    # 获取所有entity文件
    entity_dir = "equipment/src/main/java/org/simple/equipment/entity"
    java_files = glob.glob(f"{entity_dir}/*.java")
    
    print(f"开始修复 {len(java_files)} 个entity文件...\n")
    
    for file_path in sorted(java_files):
        try:
            complete_fix_entity_file(file_path)
        except Exception as e:
            print(f"  ❌ 修复失败: {e}")
        print()
    
    print("所有文件修复完成！")

if __name__ == "__main__":
    main()
