#!/usr/bin/env python3
import os
import re
import glob

def fix_entity_file(file_path):
    """修复单个entity文件"""
    print(f"正在处理: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否需要修改
    if 'extends BaseEntity' not in content:
        print(f"  - 跳过，文件不继承BaseEntity")
        return
    
    # 1. 替换类声明
    if 'implements Serializable' in content:
        # 已经实现Serializable，只需移除extends BaseEntity
        content = re.sub(r'extends BaseEntity implements Serializable', 'implements Serializable', content)
    else:
        # 替换extends BaseEntity为implements Serializable
        content = re.sub(r'extends BaseEntity', 'implements Serializable', content)
    
    # 2. 移除@EqualsAndHashCode(callSuper = true)或替换为false
    content = re.sub(r'@EqualsAndHashCode\(callSuper = true\)', '', content)
    
    # 3. 移除BaseEntity导入
    content = re.sub(r'import org\.simple\.base\.dto\.BaseEntity;\n', '', content)
    
    # 4. 添加必要的导入
    imports_to_add = []
    
    if 'import java.io.Serializable;' not in content:
        imports_to_add.append('import java.io.Serializable;')
    
    if 'import java.io.Serial;' not in content:
        imports_to_add.append('import java.io.Serial;')
    
    if 'import com.baomidou.mybatisplus.annotation.FieldFill;' not in content:
        imports_to_add.append('import com.baomidou.mybatisplus.annotation.FieldFill;')
    
    if 'import io.swagger.v3.oas.annotations.media.Schema;' not in content:
        imports_to_add.append('import io.swagger.v3.oas.annotations.media.Schema;')
    
    # 在package声明后添加导入
    if imports_to_add:
        package_match = re.search(r'(package [^;]+;)', content)
        if package_match:
            package_line = package_match.group(1)
            imports_str = '\n' + '\n'.join(imports_to_add)
            content = content.replace(package_line, package_line + imports_str)
    
    # 5. 添加序列化版本号（如果不存在）
    if 'serialVersionUID' not in content:
        # 在类声明后添加
        class_pattern = r'(public class \w+[^{]*\{)'
        if re.search(class_pattern, content):
            content = re.sub(class_pattern, r'\1\n\n    @Serial\n    private static final long serialVersionUID = 1L;', content)
    
    # 6. 添加BaseEntity字段（如果不存在）
    base_fields = [
        '    /**\n     * 创建时间\n     */\n    @TableField(fill = FieldFill.INSERT)\n    @Schema(description = "创建时间")\n    private LocalDateTime createTime;',
        '    /**\n     * 创建人id\n     */\n    @TableField(fill = FieldFill.INSERT)\n    @Schema(description = "创建人id")\n    private String creator;',
        '    /**\n     * 修改时间\n     */\n    @TableField(fill = FieldFill.INSERT_UPDATE)\n    @Schema(description = "修改时间")\n    private LocalDateTime updateTime;'
    ]
    
    # 检查是否已有这些字段
    has_create_time = 'createTime' in content
    has_creator = 'private String creator' in content
    has_update_time = 'updateTime' in content
    
    if not (has_create_time and has_creator and has_update_time):
        # 在最后一个字段后添加BaseEntity字段
        # 找到类的最后一个字段
        last_field_pattern = r'(private [^;]+;)(\s*\n\s*(?:/\*\*|public enum|\}))'
        match = None
        for match in re.finditer(last_field_pattern, content):
            pass  # 找到最后一个匹配
        
        if match:
            fields_to_add = []
            if not has_create_time:
                fields_to_add.append(base_fields[0])
            if not has_creator:
                fields_to_add.append(base_fields[1])
            if not has_update_time:
                fields_to_add.append(base_fields[2])
            
            if fields_to_add:
                fields_str = '\n\n' + '\n\n'.join(fields_to_add)
                content = content[:match.end(1)] + fields_str + content[match.start(2):]
    
    # 7. 添加@Schema注解到类上（如果不存在）
    if '@Schema(description' not in content:
        class_name_match = re.search(r'public class (\w+)', content)
        if class_name_match:
            class_name = class_name_match.group(1)
            # 在@TableName前添加@Schema
            content = re.sub(r'(@TableName[^)]+\))', f'@Schema(description = "{class_name}")\n\\1', content)
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"  - 完成处理")

def main():
    # 获取所有继承BaseEntity的entity文件
    entity_dir = "equipment/src/main/java/org/simple/equipment/entity"
    java_files = glob.glob(f"{entity_dir}/*.java")
    
    files_to_fix = []
    for file_path in java_files:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            if 'extends BaseEntity' in content:
                files_to_fix.append(file_path)
    
    print(f"找到 {len(files_to_fix)} 个需要修改的文件")
    
    for file_path in files_to_fix:
        try:
            fix_entity_file(file_path)
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {e}")
    
    print("所有文件处理完成！")

if __name__ == "__main__":
    main()
