#!/bin/bash

# 获取所有继承BaseEntity的entity文件
files=$(grep -l "extends BaseEntity" equipment/src/main/java/org/simple/equipment/entity/*.java)

echo "需要修改的文件列表："
echo "$files"

# 对每个文件进行处理
for file in $files; do
    echo "正在处理: $file"
    
    # 检查是否已经实现了Serializable
    if grep -q "implements Serializable" "$file"; then
        echo "  - 文件已实现Serializable，只需移除BaseEntity继承"
        # 移除extends BaseEntity，保留implements Serializable
        sed -i '' 's/extends BaseEntity implements Serializable/implements Serializable/g' "$file"
        sed -i '' 's/@EqualsAndHashCode(callSuper = true)/@EqualsAndHashCode(callSuper = false)/g' "$file"
    else
        echo "  - 文件未实现Serializable，需要完整替换"
        # 替换extends BaseEntity为implements Serializable
        sed -i '' 's/extends BaseEntity/implements Serializable/g' "$file"
        sed -i '' 's/@EqualsAndHashCode(callSuper = true)/@EqualsAndHashCode(callSuper = false)/g' "$file"
    fi
    
    # 移除BaseEntity导入
    sed -i '' '/import org.simple.base.dto.BaseEntity;/d' "$file"
    
    # 添加必要的导入（如果不存在）
    if ! grep -q "import java.io.Serializable;" "$file"; then
        sed -i '' '/^package/a\
\
import java.io.Serializable;' "$file"
    fi
    
    if ! grep -q "import java.io.Serial;" "$file"; then
        sed -i '' '/import java.io.Serializable;/a\
import java.io.Serial;' "$file"
    fi
    
    echo "  - 完成处理: $file"
done

echo "所有文件处理完成！"
