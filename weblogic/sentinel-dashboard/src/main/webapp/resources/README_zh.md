# Sentinel Dashboard Frontend

## 环境要求

- Node.js > 6.x
- Node.js < 12.x

## 编码规范

- HTML/CSS 遵循 [Bootstrap 编码规范](https://codeguide.bootcss.com/)
- JavaScript 遵循 [Airbnb JavaScript Style Guide](https://github.com/airbnb/javascript/tree/es5-deprecated/es5) 以及最新的 ES 6 标准

## 安装依赖

```shell
npm i
```

## 开始本地开发

```shell
npm start
```

## 构建前端资源

```shell
npm run build
```

## Credit

- [sb-admin-angular](https://github.com/start-angular/sb-admin-angular)
