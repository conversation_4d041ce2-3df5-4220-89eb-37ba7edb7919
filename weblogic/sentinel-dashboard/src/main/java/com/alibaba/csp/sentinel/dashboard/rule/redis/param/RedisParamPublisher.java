package com.alibaba.csp.sentinel.dashboard.rule.redis.param;

import com.alibaba.csp.sentinel.dashboard.datasource.entity.rule.ParamFlowRuleEntity;
import com.alibaba.csp.sentinel.dashboard.rule.Constants;
import com.alibaba.csp.sentinel.dashboard.rule.DynamicRulePublisher;
import com.alibaba.fastjson.JSON;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.List;

@Component("redisParamPublisher")
public class RedisParamPublisher implements DynamicRulePublisher<List<ParamFlowRuleEntity>> {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;


    @Override
    public void publish(String app, List<ParamFlowRuleEntity> rules) throws Exception {
        Assert.notNull(app, "应用名称不能为空");
        String ruleKey = Constants.RULE_PARAM_PREFIX;
        String ruleStr = JSON.toJSONString(rules);
        // 数据存储
        redisTemplate.opsForValue().set(ruleKey, ruleStr);
        // 数据发布
        redisTemplate.convertAndSend(Constants.RULE_PARAM_CHANNEL_PREFIX, ruleStr);
    }
}
