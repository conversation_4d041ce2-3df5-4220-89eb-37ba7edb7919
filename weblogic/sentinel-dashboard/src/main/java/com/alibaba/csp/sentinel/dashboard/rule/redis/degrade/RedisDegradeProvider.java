package com.alibaba.csp.sentinel.dashboard.rule.redis.degrade;

import com.alibaba.csp.sentinel.dashboard.datasource.entity.rule.DegradeRuleEntity;
import com.alibaba.csp.sentinel.dashboard.rule.Constants;
import com.alibaba.csp.sentinel.dashboard.rule.DynamicRuleProvider;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.fastjson.JSON;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.Collections;
import java.util.List;

@Component("redisDegradeProvider")
public class RedisDegradeProvider implements DynamicRuleProvider<List<DegradeRuleEntity>> {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;


    @Override
    public List<DegradeRuleEntity> getRules(String appName) throws Exception {
        Assert.notNull(appName, "应用名称不能为空");
        String key = Constants.RULE_DEGRADE_PREFIX;
        String ruleStr = (String)redisTemplate.opsForValue().get(key);
        if(StringUtil.isEmpty(ruleStr)) {
            return Collections.emptyList();
        }
        return JSON.parseArray(ruleStr, DegradeRuleEntity.class);
    }
}
