package com.alibaba.csp.sentinel.dashboard.rule;

public final class Constants {

    // 最终规则是存储的key
    public static final String RULE_FLOW_PREFIX = "sentinel:rule:flow";

    // Redis的订阅发布功能，需要一个通道
    public static final String RULE_FLOW_CHANNEL_PREFIX = "sentinel:channel:flow";

    // 最终规则是存储的key
    public static final String RULE_DEGRADE_PREFIX = "sentinel:rule:degrade";

    // Redis的订阅发布功能，需要一个通道
    public static final String RULE_DEGRADE_CHANNEL_PREFIX = "sentinel:channel:degrade";

    // 最终规则是存储的key
    public static final String RULE_PARAM_PREFIX = "sentinel:rule:param";

    // Redis的订阅发布功能，需要一个通道
    public static final String RULE_PARAM_CHANNEL_PREFIX = "sentinel:channel:param";

    // 最终规则是存储的key
    public static final String RULE_SYSTEM_PREFIX = "sentinel:rule:system";

    // Redis的订阅发布功能，需要一个通道
    public static final String RULE_SYSTEM_CHANNEL_PREFIX = "sentinel:channel:system";

    // 最终规则是存储的key
    public static final String RULE_AUTH_PREFIX = "sentinel:rule:auth";

    // Redis的订阅发布功能，需要一个通道
    public static final String RULE_AUTH_CHANNEL_PREFIX = "sentinel:channel:auth";


    // 每一个规则都需要唯一id，基于Redis生成id
    public static final String RULE_FLOW_ID_KEY = "sentinel:id:flow";
}
