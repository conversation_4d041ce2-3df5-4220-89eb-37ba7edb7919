FROM amd64/buildpack-deps:buster-curl as installer

ARG SENTINEL_VERSION=1.8.8

RUN set -x \
    && curl -SL --output /home/<USER>//github.com/alibaba/Sentinel/releases/download/${SENTINEL_VERSION}/sentinel-dashboard-${SENTINEL_VERSION}.jar

FROM openjdk:8-jre-slim

# copy sentinel jar
COPY --from=installer ["/home/<USER>", "/home/<USER>"]

ENV JAVA_OPTS '-Dserver.port=8080 -Dcsp.sentinel.dashboard.server=localhost:8080'

RUN chmod -R +x /home/<USER>

EXPOSE 8080

CMD java ${JAVA_OPTS} -jar /home/<USER>

